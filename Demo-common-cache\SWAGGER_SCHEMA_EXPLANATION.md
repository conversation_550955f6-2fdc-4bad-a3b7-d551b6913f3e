# Swagger Schema 詳解

## 📋 **什麼是 Schema？**

**Schema** 是 OpenAPI/Swagger 規範中用來定義**數據結構**的部分，它描述了 API 中使用的所有數據模型。

## 🎯 **Schema 的位置**

在 Swagger UI 中，Schema 通常位於頁面**最下方**，標題為 **"Schemas"** 或 **"Models"**。

## 🔧 **Schema 的功用**

### **1. 數據模型定義**
```json
{
  "User": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string",
        "description": "用戶唯一標識"
      },
      "username": {
        "type": "string",
        "description": "用戶名"
      },
      "email": {
        "type": "string",
        "format": "email",
        "description": "電子郵件"
      }
    },
    "required": ["id", "username"]
  }
}
```

### **2. 請求/回應結構**
Schema 定義了：
- **請求體 (Request Body)** 的結構
- **回應 (Response)** 的數據格式
- **參數 (Parameters)** 的類型和約束

### **3. 數據驗證**
- **類型檢查**: 確保數據類型正確
- **格式驗證**: 如 email、date、uuid 等
- **必填欄位**: 標記哪些欄位是必需的
- **數值範圍**: 最小值、最大值、長度限制

## 📊 **Schema 的組成部分**

### **基本類型**
| 類型 | 說明 | 範例 |
|------|------|------|
| `string` | 字符串 | `"Hello World"` |
| `integer` | 整數 | `42` |
| `number` | 數字 | `3.14` |
| `boolean` | 布林值 | `true` / `false` |
| `array` | 陣列 | `[1, 2, 3]` |
| `object` | 物件 | `{"key": "value"}` |

### **格式約束**
| 格式 | 說明 | 範例 |
|------|------|------|
| `email` | 電子郵件格式 | `<EMAIL>` |
| `date` | 日期格式 | `2025-07-22` |
| `date-time` | 日期時間格式 | `2025-07-22T15:30:00Z` |
| `uuid` | UUID 格式 | `123e4567-e89b-12d3-a456-************` |
| `password` | 密碼 (在 UI 中隱藏) | `********` |

## 🎨 **在 Demo Common Cache 中的 Schema**

### **Session Schema 範例**
```json
{
  "SessionInfo": {
    "type": "object",
    "properties": {
      "jwtId": {
        "type": "string",
        "format": "uuid",
        "description": "JWT 唯一標識"
      },
      "userId": {
        "type": "string",
        "description": "用戶 ID"
      },
      "username": {
        "type": "string",
        "description": "用戶名"
      },
      "createdAt": {
        "type": "string",
        "format": "date-time",
        "description": "創建時間"
      },
      "expiresAt": {
        "type": "string",
        "format": "date-time",
        "description": "過期時間"
      },
      "ttl": {
        "type": "integer",
        "minimum": 0,
        "description": "剩餘生存時間（秒）"
      }
    },
    "required": ["jwtId", "userId", "username"]
  }
}
```

### **Cache Schema 範例**
```json
{
  "CacheEntry": {
    "type": "object",
    "properties": {
      "key": {
        "type": "string",
        "description": "快取鍵"
      },
      "value": {
        "type": "object",
        "description": "快取值"
      },
      "ttl": {
        "type": "integer",
        "minimum": -1,
        "description": "生存時間（秒），-1 表示永不過期"
      },
      "createdAt": {
        "type": "string",
        "format": "date-time",
        "description": "創建時間"
      }
    },
    "required": ["key", "value"]
  }
}
```

### **API Response Schema 範例**
```json
{
  "ApiResponse": {
    "type": "object",
    "properties": {
      "success": {
        "type": "boolean",
        "description": "操作是否成功"
      },
      "message": {
        "type": "string",
        "description": "回應訊息"
      },
      "data": {
        "type": "object",
        "description": "回應數據"
      },
      "timestamp": {
        "type": "string",
        "format": "date-time",
        "description": "回應時間"
      }
    },
    "required": ["success", "message"]
  }
}
```

## 🚀 **Schema 的實際用途**

### **1. 開發者參考**
- 📖 **快速理解**: 一目了然地看到數據結構
- 🔧 **代碼生成**: 可以根據 Schema 自動生成客戶端代碼
- 📝 **文檔說明**: 提供詳細的欄位說明和約束

### **2. 前端開發**
- 🎯 **TypeScript 類型**: 可以轉換為 TypeScript 介面
- ✅ **表單驗證**: 根據 Schema 進行前端驗證
- 🔄 **數據轉換**: 確保前後端數據格式一致

### **3. 測試和驗證**
- 🧪 **自動化測試**: 根據 Schema 生成測試數據
- ✅ **數據驗證**: 確保 API 回應符合預期格式
- 🐛 **錯誤檢測**: 及早發現數據格式問題

### **4. API 工具整合**
- 📡 **Postman**: 可以導入 Schema 進行 API 測試
- 🔧 **代碼生成器**: 如 OpenAPI Generator
- 📊 **Mock 服務**: 根據 Schema 生成模擬數據

## 💡 **Schema 的優勢**

### **✅ 標準化**
- 統一的數據格式定義
- 跨團隊的一致性
- 減少溝通成本

### **✅ 自動化**
- 自動生成文檔
- 自動驗證數據
- 自動生成代碼

### **✅ 可維護性**
- 集中管理數據模型
- 版本控制和變更追蹤
- 向後相容性檢查

## 🔍 **如何查看 Schema**

### **在 Swagger UI 中**
1. 打開 Swagger UI: `http://localhost:8080/swagger-ui.html`
2. 滾動到頁面最下方
3. 找到 **"Schemas"** 或 **"Models"** 部分
4. 點擊任意 Schema 名稱查看詳細結構

### **在 API 文檔 JSON 中**
```bash
# 獲取完整的 OpenAPI 文檔
curl http://localhost:8080/api-docs

# 查看 components.schemas 部分
curl http://localhost:8080/api-docs | jq '.components.schemas'
```

## 📝 **最佳實踐**

### **1. 命名規範**
- 使用 **PascalCase**: `UserInfo`, `SessionData`
- 描述性名稱: `CreateUserRequest`, `LoginResponse`
- 避免縮寫: 使用 `UserInformation` 而不是 `UserInfo`

### **2. 結構設計**
- **單一職責**: 每個 Schema 只描述一種數據結構
- **可重用性**: 設計可以在多個 API 中重用的 Schema
- **擴展性**: 考慮未來可能的欄位擴展

### **3. 文檔完整性**
- 為每個欄位添加 `description`
- 標記 `required` 欄位
- 提供 `example` 值
- 設定適當的 `format` 約束

## 🎉 **總結**

**Schema 是 Swagger 的核心組成部分**，它：

- 📋 **定義數據結構**: 描述 API 中所有數據模型
- 🔧 **提供驗證**: 確保數據格式正確
- 📖 **改善文檔**: 讓 API 文檔更清晰易懂
- 🚀 **支援工具**: 與各種開發工具整合
- ✅ **提高品質**: 減少前後端溝通錯誤

**Schema 讓您的 API 更加專業、可靠和易於使用！** 🎯
