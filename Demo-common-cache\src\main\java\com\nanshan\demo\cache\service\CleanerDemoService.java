package com.nanshan.demo.cache.service;

import com.nanshan.common.cache.service.CacheManager;
import com.nanshan.common.cache.service.RedisCleaner;
import com.nanshan.common.cache.service.SessionManager;
import com.nanshan.demo.cache.model.Product;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 清理器示範服務
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CleanerDemoService {

    private final RedisCleaner redisCleaner;
    private final CacheManager cacheManager;
    private final SessionManager sessionManager;

    /**
     * 手動清理示範
     *
     * @return 示範結果
     */
    public Map<String, Object> manualCleanupDemo() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("開始手動清理示範");

            // 1. 建立一些測試資料
            createTestData();

            // 2. 執行手動清理
            RedisCleaner.CleanupResult cleanupResult = redisCleaner.performManualCleanup();

            // 3. 獲取清理統計
            RedisCleaner.CleanupStats stats = redisCleaner.getCleanupStats();

            // 返回結果
            result.put("success", true);
            result.put("message", "手動清理示範完成");

            // 安全地構建清理結果 Map
            Map<String, Object> cleanupResultMap = new HashMap<>();
            cleanupResultMap.put("sessionsCleaned", cleanupResult.getSessionsCleaned());
            cleanupResultMap.put("cachesCleaned", cleanupResult.getCachesCleaned());
            cleanupResultMap.put("locksCleaned", cleanupResult.getLocksCleaned());
            cleanupResultMap.put("durationMs", cleanupResult.getDurationMs());
            cleanupResultMap.put("isSuccess", cleanupResult.isSuccess());
            cleanupResultMap.put("errorMessage", cleanupResult.getErrorMessage() != null ? cleanupResult.getErrorMessage() : "");
            result.put("cleanupResult", cleanupResultMap);

            // 安全地構建統計 Map
            Map<String, Object> statsMap = new HashMap<>();
            statsMap.put("totalCleanups", stats.getTotalCleanups());
            statsMap.put("totalSessionsCleaned", stats.getTotalSessionsCleaned());
            statsMap.put("totalCachesCleaned", stats.getTotalCachesCleaned());
            statsMap.put("totalLocksCleaned", stats.getTotalLocksCleaned());
            statsMap.put("lastCleanupTime", stats.getLastCleanupTime() != null ? stats.getLastCleanupTime().toString() : "未執行過清理");
            statsMap.put("averageCleanupDuration", stats.getAverageCleanupDuration());
            result.put("cleanupStats", statsMap);

            log.info("手動清理示範完成，清理結果: Sessions={}, Caches={}, Locks={}",
                    cleanupResult.getSessionsCleaned(),
                    cleanupResult.getCachesCleaned(),
                    cleanupResult.getLocksCleaned());

        } catch (Exception e) {
            log.error("手動清理示範失敗", e);
            result.put("success", false);
            result.put("message", "手動清理示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 分類清理示範
     *
     * @return 示範結果
     */
    public Map<String, Object> categorizedCleanupDemo() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("開始分類清理示範");

            // 1. 建立測試資料
            createTestData();

            // 2. 分別清理不同類型的過期資料
            long expiredSessions = redisCleaner.cleanupExpiredSessions();
            long expiredCaches = redisCleaner.cleanupExpiredCaches();
            long expiredLocks = redisCleaner.cleanupExpiredLocks();

            // 3. 清理所有過期資料
            long totalExpired = redisCleaner.cleanupAllExpired();

            // 返回結果
            result.put("success", true);
            result.put("message", "分類清理示範完成");
            result.put("expiredSessions", expiredSessions);
            result.put("expiredCaches", expiredCaches);
            result.put("expiredLocks", expiredLocks);
            result.put("totalExpired", totalExpired);

            log.info("分類清理示範完成，Sessions={}, Caches={}, Locks={}, Total={}",
                    expiredSessions, expiredCaches, expiredLocks, totalExpired);

        } catch (Exception e) {
            log.error("分類清理示範失敗", e);
            result.put("success", false);
            result.put("message", "分類清理示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 模式刪除示範
     *
     * @return 示範結果
     */
    public Map<String, Object> patternDeleteDemo() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("開始模式刪除示範");

            // 1. 建立測試資料
            createPatternTestData();

            // 2. 使用模式刪除特定的快取
            long deletedDemo = redisCleaner.deleteByPattern("demo:cache:pattern:*");

            // 3. 使用模式刪除測試 Session
            long deletedTestSessions = redisCleaner.deleteByPattern("demo:session:test:*");

            // 返回結果
            result.put("success", true);
            result.put("message", "模式刪除示範完成");
            result.put("deletedDemoCache", deletedDemo);
            result.put("deletedTestSessions", deletedTestSessions);

            log.info("模式刪除示範完成，Demo Cache={}, Test Sessions={}", deletedDemo, deletedTestSessions);

        } catch (Exception e) {
            log.error("模式刪除示範失敗", e);
            result.put("success", false);
            result.put("message", "模式刪除示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * TTL 管理示範
     *
     * @return 示範結果
     */
    public Map<String, Object> ttlManagementDemo() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("開始 TTL 管理示範");

            // 1. 建立測試快取
            String cacheKey = "demo:cache:ttl-test";
            Product product = createDemoProduct("TTL-TEST-001");
            cacheManager.put("ttl-test", product.getProductId(), product);

            // 2. 設定 TTL
            boolean ttlSet = redisCleaner.setTimeToLive(cacheKey, 300); // 5 分鐘

            // 3. 批量設定 TTL
            Set<String> keys = Set.of(
                    "demo:cache:batch-ttl-1",
                    "demo:cache:batch-ttl-2",
                    "demo:cache:batch-ttl-3"
            );

            // 先建立這些 Key
            for (String key : keys) {
                cacheManager.put("batch-ttl", key.substring(key.lastIndexOf('-') + 1),
                        createDemoProduct("BATCH-" + key.substring(key.lastIndexOf('-') + 1)));
            }

            long batchTtlSet = redisCleaner.setBatchTimeToLive(keys, 600); // 10 分鐘

            // 4. 移除 TTL (設為永不過期)
            boolean ttlRemoved = redisCleaner.removeTimeToLive(cacheKey);

            // 返回結果
            result.put("success", true);
            result.put("message", "TTL 管理示範完成");
            result.put("ttlSet", ttlSet);
            result.put("batchTtlSet", batchTtlSet);
            result.put("ttlRemoved", ttlRemoved);

            log.info("TTL 管理示範完成，TTL Set={}, Batch TTL Set={}, TTL Removed={}",
                    ttlSet, batchTtlSet, ttlRemoved);

        } catch (Exception e) {
            log.error("TTL 管理示範失敗", e);
            result.put("success", false);
            result.put("message", "TTL 管理示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 清理統計示範
     *
     * @return 示範結果
     */
    public Map<String, Object> cleanupStatsDemo() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("開始清理統計示範");

            // 1. 獲取當前統計
            RedisCleaner.CleanupStats statsBefore = redisCleaner.getCleanupStats();

            // 2. 執行一些清理操作
            createTestData();
            redisCleaner.performManualCleanup();

            // 3. 再次獲取統計
            RedisCleaner.CleanupStats statsAfter = redisCleaner.getCleanupStats();

            // 4. 重置統計
            redisCleaner.resetCleanupStats();

            // 5. 獲取重置後的統計
            RedisCleaner.CleanupStats statsAfterReset = redisCleaner.getCleanupStats();

            // 返回結果
            result.put("success", true);
            result.put("message", "清理統計示範完成");

            // 安全地構建統計前 Map
            Map<String, Object> statsBeforeMap = new HashMap<>();
            statsBeforeMap.put("totalCleanups", statsBefore.getTotalCleanups());
            statsBeforeMap.put("totalSessionsCleaned", statsBefore.getTotalSessionsCleaned());
            statsBeforeMap.put("totalCachesCleaned", statsBefore.getTotalCachesCleaned());
            statsBeforeMap.put("totalLocksCleaned", statsBefore.getTotalLocksCleaned());
            statsBeforeMap.put("lastCleanupTime", statsBefore.getLastCleanupTime() != null ? statsBefore.getLastCleanupTime().toString() : "未執行過清理");
            statsBeforeMap.put("averageCleanupDuration", statsBefore.getAverageCleanupDuration());
            result.put("statsBefore", statsBeforeMap);

            // 安全地構建統計後 Map
            Map<String, Object> statsAfterMap = new HashMap<>();
            statsAfterMap.put("totalCleanups", statsAfter.getTotalCleanups());
            statsAfterMap.put("totalSessionsCleaned", statsAfter.getTotalSessionsCleaned());
            statsAfterMap.put("totalCachesCleaned", statsAfter.getTotalCachesCleaned());
            statsAfterMap.put("totalLocksCleaned", statsAfter.getTotalLocksCleaned());
            statsAfterMap.put("lastCleanupTime", statsAfter.getLastCleanupTime() != null ? statsAfter.getLastCleanupTime().toString() : "未執行過清理");
            statsAfterMap.put("averageCleanupDuration", statsAfter.getAverageCleanupDuration());
            result.put("statsAfter", statsAfterMap);

            // 安全地構建重置後統計 Map
            Map<String, Object> statsAfterResetMap = new HashMap<>();
            statsAfterResetMap.put("totalCleanups", statsAfterReset.getTotalCleanups());
            statsAfterResetMap.put("totalSessionsCleaned", statsAfterReset.getTotalSessionsCleaned());
            statsAfterResetMap.put("totalCachesCleaned", statsAfterReset.getTotalCachesCleaned());
            statsAfterResetMap.put("totalLocksCleaned", statsAfterReset.getTotalLocksCleaned());
            statsAfterResetMap.put("lastCleanupTime", statsAfterReset.getLastCleanupTime() != null ? statsAfterReset.getLastCleanupTime().toString() : "未執行過清理");
            statsAfterResetMap.put("averageCleanupDuration", statsAfterReset.getAverageCleanupDuration());
            result.put("statsAfterReset", statsAfterResetMap);

            log.info("清理統計示範完成");

        } catch (Exception e) {
            log.error("清理統計示範失敗", e);
            result.put("success", false);
            result.put("message", "清理統計示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 建立測試資料
     */
    private void createTestData() {
        // 建立一些短期快取 (很快過期)
        for (int i = 1; i <= 3; i++) {
            Product product = createDemoProduct("SHORT-LIVED-" + i);
            cacheManager.put("test", product.getProductId(), product, 1, TimeUnit.SECONDS);
        }

        // 建立一些正常快取
        for (int i = 1; i <= 2; i++) {
            Product product = createDemoProduct("NORMAL-" + i);
            cacheManager.put("test", product.getProductId(), product, 3600, TimeUnit.SECONDS);
        }

        // 等待短期快取過期
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 建立模式測試資料
     */
    private void createPatternTestData() {
        // 建立模式快取資料
        for (int i = 1; i <= 3; i++) {
            Product product = createDemoProduct("PATTERN-" + i);
            cacheManager.put("pattern", product.getProductId(), product);
        }
    }

    /**
     * 建立示範產品
     */
    private Product createDemoProduct(String productId) {
        return Product.builder()
                .productId(productId)
                .name("清理器測試產品 " + productId)
                .description("用於清理器示範的測試產品")
                .price(new BigDecimal("88.88"))
                .category("測試分類")
                .tags(Arrays.asList("清理器", "測試"))
                .stock(50)
                .enabled(true)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .supplier("測試供應商")
                .specifications("測試規格")
                .build();
    }
}
