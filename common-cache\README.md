# Common Cache

[![Maven Central](https://img.shields.io/maven-central/v/com.nanshan/common-cache.svg)](https://search.maven.org/artifact/com.nanshan/common-cache)
[![Java Version](https://img.shields.io/badge/Java-17+-blue.svg)](https://openjdk.java.net/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2+-green.svg)](https://spring.io/projects/spring-boot)
[![Redis](https://img.shields.io/badge/Redis-6.0+-red.svg)](https://redis.io/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 🚀 **企業級 Redis 快取解決方案** - 基於 Spring Boot 3.x 和 Redisson 的高效能分散式快取框架

## 📋 **目錄**

- [專案概述](#專案概述)
- [核心功能](#核心功能)
- [最新修復](#最新修復)
- [快速開始](#快速開始)
- [配置說明](#配置說明)
- [API 使用](#api-使用)
- [測試指南](#測試指南)
- [故障排除](#故障排除)
- [貢獻指南](#貢獻指南)

## 🎯 **專案概述**

Common Cache 是一個基於 Redis 和 Redisson 的企業級快取解決方案，專為 Spring Boot 應用程式設計。它提供了統一的快取管理介面，支援分散式環境下的高效能資料快取、會話管理和分散式鎖功能。

### **主要特色**

- 🔥 **高效能**: 基於 Redisson 的非阻塞 I/O 操作
- 🛡️ **企業級**: 支援生產環境保護和危險操作控制
- 🔧 **易配置**: 透過 YAML 配置檔案輕鬆設定
- 🧪 **完整測試**: 包含單元測試和整合測試
- 📊 **監控友善**: 內建日誌記錄和效能監控
- 🌐 **分散式**: 原生支援分散式環境和叢集部署

### **技術棧**

- **Java 17+**: 現代 Java 特性支援
- **Spring Boot 3.2+**: 最新的 Spring 生態系統
- **Redisson 3.24+**: 高效能 Redis 客戶端
- **Jackson 2.15+**: JSON 序列化/反序列化
- **JUnit 5**: 現代化測試框架

## ⚡ **核心功能**

### **1. 快取管理 (CacheManager)**
- ✅ 基本 CRUD 操作 (get, put, remove)
- ✅ TTL (生存時間) 管理
- ✅ 批次操作支援
- ✅ 快取統計和監控
- ✅ 自動過期清理

### **2. 會話管理 (SessionManager)**
- ✅ JWT 會話儲存和檢索
- ✅ 會話自動續期
- ✅ 多索引查詢 (userId, clientId)
- ✅ 會話狀態管理
- ✅ 安全的會話清理

### **3. 分散式鎖 (LockManager)**
- ✅ 公平鎖和非公平鎖
- ✅ 讀寫鎖支援
- ✅ 鎖超時和自動釋放
- ✅ 死鎖檢測和預防
- ✅ 鎖狀態監控

### **4. 資料清理 (RedisCleaner)**
- ✅ 過期資料自動清理
- ✅ 批次清理操作
- ✅ 清理統計報告
- ✅ 可配置清理策略
- ✅ 生產環境保護

### **5. 環境保護 (EnvGuard)**
- ✅ 生產環境檢測
- ✅ 危險操作控制
- ✅ 操作權限管理
- ✅ 安全日誌記錄

## 🔧 **最新修復**

### **JSON 解析控制字符問題修復 (v1.0.0)**

**問題描述**: 
在某些情況下，Redis 中可能存在包含控制字符的 JSON 資料，導致 Jackson 解析失敗：
```
JsonParseException: Illegal character ((CTRL-CHAR, code 3)): 
only regular white space (\r, \n, \t) is allowed between tokens
```

**修復方案**:
我們強化了 ObjectMapper 配置，使其能夠處理控制字符：

```java
// 關鍵配置
objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
objectMapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);
```

**修復效果**:
- ✅ 完全解決控制字符解析問題
- ✅ 提高 JSON 解析容錯性
- ✅ 保持向後相容性
- ✅ 通過完整測試驗證

## 🚀 **快速開始**

### **1. 環境要求**

- Java 17 或更高版本
- Maven 3.6+ 或 Gradle 7+
- Redis 6.0+ 伺服器
- Spring Boot 3.2+

### **2. Maven 依賴**

```xml
<dependency>
    <groupId>com.nanshan</groupId>
    <artifactId>common-cache</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### **3. 啟用快取支援**

在主應用程式類別上添加註解：

```java
@SpringBootApplication
@EnableRedisSupport  // 啟用 Redis 支援
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### **4. 基本配置**

在 `application.yml` 中配置 Redis 連線：

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: your-password
    database: 0

common:
  cache:
    cache:
      default-time-to-live: 300  # 預設 TTL (秒)
    session:
      default-time-to-live: 1800  # 會話 TTL (秒)
    lock:
      default-wait-time: 10      # 鎖等待時間 (秒)
      default-lease-time: 30     # 鎖持有時間 (秒)
```

## ⚙️ **配置說明**

### **完整配置範例**

```yaml
# Redis 基本配置
spring:
  redis:
    host: localhost
    port: 6379
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

# Common Cache 配置
common:
  cache:
    # 快取配置
    cache:
      default-time-to-live: 300      # 預設快取 TTL (秒)
      max-idle-time: 600             # 最大閒置時間 (秒)
      
    # 會話配置  
    session:
      default-time-to-live: 1800     # 預設會話 TTL (秒)
      auto-renewal: true             # 自動續期
      renewal-threshold: 0.5         # 續期閾值 (50%)
      
    # 鎖配置
    lock:
      default-wait-time: 10          # 預設等待時間 (秒)
      default-lease-time: 30         # 預設持有時間 (秒)
      fair-lock-enabled: true        # 啟用公平鎖
      
    # 清理器配置
    cleaner:
      enabled: true                  # 啟用自動清理
      batch-size: 100                # 批次大小
      
    # 環境保護配置
    env-guard:
      production-environment: false  # 是否為生產環境
      dangerous-operations-enabled: true  # 是否允許危險操作
```

### **配置參數說明**

| 參數 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| `cache.default-time-to-live` | int | 300 | 快取預設 TTL (秒) |
| `session.auto-renewal` | boolean | true | 是否啟用會話自動續期 |
| `lock.fair-lock-enabled` | boolean | true | 是否啟用公平鎖 |
| `cleaner.enabled` | boolean | true | 是否啟用自動清理 |
| `env-guard.production-environment` | boolean | false | 生產環境標識 |

## 💻 **API 使用**

### **1. 快取管理 (CacheManager)**

```java
@Service
public class UserService {

    @Autowired
    private CacheManager cacheManager;

    // 基本快取操作
    public void cacheUser(User user) {
        cacheManager.put("user", user.getId(), user, 300); // TTL 300秒
    }

    public User getUser(String userId) {
        return cacheManager.get("user", userId, User.class);
    }

    public void removeUser(String userId) {
        cacheManager.remove("user", userId);
    }

    // 批次操作
    public Map<String, User> getUsers(Set<String> userIds) {
        return cacheManager.getMultiple("user", userIds, User.class);
    }

    // 檢查快取存在
    public boolean userExists(String userId) {
        return cacheManager.exists("user", userId);
    }

    // 獲取剩餘 TTL
    public long getUserTTL(String userId) {
        return cacheManager.getRemainingTimeToLive("user", userId, TimeUnit.SECONDS);
    }
}
```

### **2. 會話管理 (SessionManager)**

```java
@Service
public class AuthService {

    @Autowired
    private SessionManager sessionManager;

    // 儲存會話
    public void saveUserSession(String jwtId, SessionObject session) {
        sessionManager.saveSession(jwtId, session, Duration.ofHours(2));
    }

    // 載入會話
    public Optional<SessionObject> getUserSession(String jwtId) {
        return sessionManager.loadSession(jwtId);
    }

    // 會話續期
    public void renewSession(String jwtId) {
        sessionManager.renewSession(jwtId, Duration.ofHours(2));
    }

    // 根據使用者 ID 查詢會話
    public Set<String> getUserSessions(String userId) {
        return sessionManager.findSessionsByUserId(userId);
    }

    // 根據客戶端 ID 查詢會話
    public Set<String> getClientSessions(String clientId) {
        return sessionManager.findSessionsByClientId(clientId);
    }

    // 刪除會話
    public void logout(String jwtId) {
        sessionManager.deleteSession(jwtId);
    }

    // 刪除使用者所有會話
    public void logoutAllSessions(String userId) {
        sessionManager.deleteSessionsByUserId(userId);
    }
}
```

### **3. 分散式鎖 (LockManager)**

```java
@Service
public class OrderService {

    @Autowired
    private LockManager lockManager;

    // 基本鎖操作
    public void processOrder(String orderId) {
        String lockKey = "order:" + orderId;

        try {
            // 嘗試獲取鎖，等待 5 秒，持有 30 秒
            boolean locked = lockManager.tryLock(lockKey, 5, 30, TimeUnit.SECONDS);

            if (locked) {
                // 執行業務邏輯
                doProcessOrder(orderId);
            } else {
                throw new RuntimeException("無法獲取訂單鎖: " + orderId);
            }
        } finally {
            // 確保釋放鎖
            lockManager.unlock(lockKey);
        }
    }

    // 讀寫鎖範例
    public String readOrderStatus(String orderId) {
        String lockKey = "order:" + orderId;

        try {
            lockManager.readLock(lockKey, 10, TimeUnit.SECONDS);
            return getOrderStatus(orderId);
        } finally {
            lockManager.readUnlock(lockKey);
        }
    }

    public void updateOrderStatus(String orderId, String status) {
        String lockKey = "order:" + orderId;

        try {
            lockManager.writeLock(lockKey, 10, TimeUnit.SECONDS);
            setOrderStatus(orderId, status);
        } finally {
            lockManager.writeUnlock(lockKey);
        }
    }

    // 檢查鎖狀態
    public boolean isOrderLocked(String orderId) {
        return lockManager.isLocked("order:" + orderId);
    }

    // 檢查是否由當前執行緒持有
    public boolean isOrderLockedByCurrentThread(String orderId) {
        return lockManager.isHeldByCurrentThread("order:" + orderId);
    }

    private void doProcessOrder(String orderId) {
        // 業務邏輯實現
    }

    private String getOrderStatus(String orderId) {
        // 讀取訂單狀態
        return "PROCESSING";
    }

    private void setOrderStatus(String orderId, String status) {
        // 更新訂單狀態
    }
}
```

### **4. 資料清理 (RedisCleaner)**

```java
@Service
public class MaintenanceService {

    @Autowired
    private RedisCleaner redisCleaner;

    // 清理過期快取
    public void cleanExpiredCache() {
        int cleaned = redisCleaner.cleanExpiredKeys("cache:*", 100);
        log.info("清理了 {} 個過期快取項目", cleaned);
    }

    // 清理特定模式的 key
    public void cleanUserCache(String userId) {
        String pattern = "user:" + userId + ":*";
        int cleaned = redisCleaner.cleanExpiredKeys(pattern, 50);
        log.info("清理了使用者 {} 的 {} 個快取項目", userId, cleaned);
    }

    // 獲取清理統計
    public void printCleaningStats() {
        // 清理統計會在日誌中顯示
        redisCleaner.cleanExpiredKeys("*", 1000);
    }
}
```

## 🧪 **測試指南**

### **1. 運行所有測試**

```bash
# 運行所有測試
mvn clean test

# 運行特定測試類
mvn test -Dtest=CacheManagerTest

# 運行特定測試方法
mvn test -Dtest=CacheManagerTest#testPutAndGet
```

### **2. 整合測試**

```bash
# 確保 Redis 服務運行
docker run -d -p 6379:6379 redis:7-alpine

# 運行整合測試
mvn test -Dtest=CommonCacheIntegrationTest
```

### **3. JSON 解析測試**

```bash
# 驗證 JSON 解析修復
mvn test -Dtest=JsonParsingTest
```

### **4. 測試覆蓋率**

```bash
# 生成測試覆蓋率報告
mvn clean test jacoco:report

# 查看報告
open target/site/jacoco/index.html
```

### **5. 效能測試**

```java
@Test
void performanceTest() {
    // 測試快取效能
    long startTime = System.currentTimeMillis();

    for (int i = 0; i < 10000; i++) {
        cacheManager.put("perf", "key" + i, "value" + i, 300);
    }

    long endTime = System.currentTimeMillis();
    System.out.println("10000 次寫入耗時: " + (endTime - startTime) + "ms");
}
```

## 🔧 **故障排除**

### **1. JSON 解析錯誤**

**問題**: `JsonParseException: Illegal character ((CTRL-CHAR, code 3))`

**解決方案**:
```bash
# 1. 確認已使用最新版本
mvn clean install

# 2. 清理 Redis 中的損壞資料
redis-cli FLUSHALL

# 3. 重新啟動應用
mvn spring-boot:run
```

### **2. Redis 連線問題**

**問題**: `Unable to connect to Redis server`

**解決方案**:
```bash
# 檢查 Redis 服務狀態
redis-cli ping

# 檢查配置
# application.yml 中的 Redis 配置是否正確

# 檢查網路連線
telnet localhost 6379
```

### **3. 鎖超時問題**

**問題**: 分散式鎖獲取超時

**解決方案**:
```java
// 增加等待時間
lockManager.tryLock(lockKey, 30, 60, TimeUnit.SECONDS);

// 檢查鎖是否被正確釋放
if (lockManager.isLocked(lockKey)) {
    lockManager.forceUnlock(lockKey);
}
```

### **4. 記憶體使用過高**

**問題**: Redis 記憶體使用過高

**解決方案**:
```bash
# 檢查 Redis 記憶體使用
redis-cli INFO memory

# 清理過期 key
redis-cli --eval cleanup.lua

# 調整 TTL 設定
# 在配置中減少 default-time-to-live 值
```

### **5. 效能問題**

**問題**: 快取操作效能低下

**解決方案**:
```yaml
# 調整連線池設定
spring:
  redis:
    lettuce:
      pool:
        max-active: 16
        max-idle: 16
        min-idle: 4

# 啟用管道操作
common:
  cache:
    redis:
      pipeline-enabled: true
```

## 📊 **監控和日誌**

### **1. 啟用詳細日誌**

```yaml
logging:
  level:
    com.nanshan.common.cache: DEBUG
    org.redisson: INFO
```

### **2. 監控指標**

```java
// 快取命中率監控
@Component
public class CacheMetrics {

    @EventListener
    public void handleCacheHit(CacheHitEvent event) {
        // 記錄快取命中
        meterRegistry.counter("cache.hit", "type", event.getCacheType()).increment();
    }

    @EventListener
    public void handleCacheMiss(CacheMissEvent event) {
        // 記錄快取未命中
        meterRegistry.counter("cache.miss", "type", event.getCacheType()).increment();
    }
}
```

## 🤝 **貢獻指南**

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

### **開發環境設定**

```bash
# 克隆專案
git clone https://github.com/your-org/common-cache.git

# 安裝依賴
mvn clean install

# 運行測試
mvn test

# 啟動 Redis (用於測試)
docker run -d -p 6379:6379 redis:7-alpine
```

## 📄 **授權條款**

本專案採用 MIT 授權條款 - 詳見 [LICENSE](LICENSE) 檔案

## 📞 **支援**

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-org/common-cache/issues)
- 📖 文檔: [Wiki](https://github.com/your-org/common-cache/wiki)

---

**Made with ❤️ by Nanshan Team**
