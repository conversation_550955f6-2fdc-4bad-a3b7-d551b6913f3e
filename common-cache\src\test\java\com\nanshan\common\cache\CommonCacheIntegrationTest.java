package com.nanshan.common.cache;

import com.nanshan.common.cache.annotation.EnableRedisSupport;
import com.nanshan.common.cache.config.TestConfig;
import com.nanshan.common.cache.model.SessionObject;
import com.nanshan.common.cache.model.SessionObjectBuilder;
import com.nanshan.common.cache.service.CacheManager;
import com.nanshan.common.cache.service.LockManager;
import com.nanshan.common.cache.service.RedisCleaner;
import com.nanshan.common.cache.service.SessionManager;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Common Cache 整合測試
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = CommonCacheIntegrationTest.TestApplication.class)
@Import(TestConfig.class)
@TestPropertySource(properties = {
    "common.cache.session.default-ttl=30",
    "common.cache.cache.default-ttl=30",
    "common.cache.lock.default-lease-time=30",
    "common.cache.cleaner.enabled=true"
})
class CommonCacheIntegrationTest {

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private RedisCleaner redisCleaner;

    @Test
    void testAllComponentsAreInjected() {
        assertNotNull(sessionManager);
        assertNotNull(cacheManager);
        assertNotNull(lockManager);
        assertNotNull(redisCleaner);
    }

    @Test
    void testSessionManagerIntegration() {
        String jwtId = "integration-test-jwt";
        SessionObject session = SessionObjectBuilder.createBasicSession("user123", "portal", jwtId);

        // 儲存 Session
        sessionManager.saveSession(jwtId, session, 60);

        // 載入 Session
        Optional<SessionObject> loaded = sessionManager.loadSession(jwtId);
        assertTrue(loaded.isPresent());
        assertEquals("user123", loaded.get().getUserId());

        // 清理
        sessionManager.deleteSession(jwtId);
    }

    @Test
    void testCacheManagerIntegration() {
        String cacheType = "user";
        String cacheId = "123";
        String cacheValue = "test-user-data";

        // 儲存快取
        cacheManager.put(cacheType, cacheId, cacheValue, 60, TimeUnit.SECONDS);

        // 獲取快取
        Optional<String> cached = cacheManager.get(cacheType, cacheId, String.class);
        assertTrue(cached.isPresent());
        assertEquals(cacheValue, cached.get());

        // 清理
        cacheManager.remove(cacheType, cacheId);
    }

    @Test
    void testLockManagerIntegration() {
        String resource = "integration-test-resource";

        // 獲取鎖
        boolean locked = lockManager.tryLock(resource, 5, 30, TimeUnit.SECONDS);
        assertTrue(locked);

        // 檢查鎖狀態
        assertTrue(lockManager.isLocked(resource));
        assertTrue(lockManager.isHeldByCurrentThread(resource));

        // 釋放鎖
        boolean unlocked = lockManager.unlock(resource);
        assertTrue(unlocked);
        assertFalse(lockManager.isLocked(resource));
    }

    @Test
    void testLockManagerExecuteWithLock() {
        String resource = "execute-test-resource";

        String result;
        try {
            boolean locked = lockManager.tryLock(resource, 5, 30, TimeUnit.SECONDS);
            assertTrue(locked);

            // 在鎖保護下執行的操作
            assertTrue(lockManager.isHeldByCurrentThread(resource));
            result = "success";
        } finally {
            lockManager.unlock(resource);
        }

        assertEquals("success", result);
        assertFalse(lockManager.isLocked(resource));
    }

    @Test
    void testRedisCleanerIntegration() {
        // 建立一些測試資料
        sessionManager.saveSession("cleanup-jwt",
                SessionObjectBuilder.createBasicSession("user", "client", "cleanup-jwt"), 1);
        cacheManager.put("cleanup", "test", "data", 1, TimeUnit.SECONDS);

        // 等待資料過期
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 執行清理
        RedisCleaner.CleanupResult result = redisCleaner.performManualCleanup();

        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getDurationMs() >= 0);

        // 獲取統計資訊
        RedisCleaner.CleanupStats stats = redisCleaner.getCleanupStats();
        assertNotNull(stats);
        assertTrue(stats.getTotalCleanups() >= 1);
    }

    @Test
    void testGetOrComputeCache() {
        String cacheType = "computed";
        String cacheId = "test";

        // 第一次調用，應該計算值
        Optional<String> opt1 = cacheManager.get(cacheType, cacheId, String.class);
        String result1;
        if (opt1.isPresent()) {
            result1 = opt1.get();
        } else {
            result1 = "computed-value";
            cacheManager.put(cacheType, cacheId, result1);
        }
        assertEquals("computed-value", result1);

        // 第二次調用，應該從快取獲取
        Optional<String> opt2 = cacheManager.get(cacheType, cacheId, String.class);
        String result2;
        if (opt2.isPresent()) {
            result2 = opt2.get();
        } else {
            result2 = "should-not-be-called";
            cacheManager.put(cacheType, cacheId, result2);
        }
        assertEquals("computed-value", result2);

        // 清理
        cacheManager.remove(cacheType, cacheId);
    }

    @SpringBootApplication
    @EnableRedisSupport
    static class TestApplication {

        public static void main(String[] args) {
            SpringApplication.run(TestApplication.class, args);
        }
    }
}
