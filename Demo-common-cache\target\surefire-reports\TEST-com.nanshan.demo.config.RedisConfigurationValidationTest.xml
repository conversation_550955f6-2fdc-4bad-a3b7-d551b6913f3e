<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.nanshan.demo.config.RedisConfigurationValidationTest" time="10.206" tests="3" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="MS950"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache\target\test-classes;C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.2.0\spring-boot-starter-web-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.2.0\spring-boot-starter-json-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.3\jackson-datatype-jdk8-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.3\jackson-module-parameter-names-2.15.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.0\spring-boot-starter-tomcat-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-10.1.16.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.16\tomcat-embed-el-10.1.16.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.16\tomcat-embed-websocket-10.1.16.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.1.1\spring-web-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.1.1\spring-webmvc-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;C:\Users\<USER>\.m2\repository\com\nanshan\common-cache\1.0.0-SNAPSHOT\common-cache-1.0.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\3.2.0\spring-boot-starter-data-redis-3.2.0.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.3.0.RELEASE\lettuce-core-6.3.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.6.0\reactor-core-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\3.2.0\spring-data-redis-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\3.2.0\spring-data-keyvalue-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.2.0\spring-data-commons-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.1.1\spring-tx-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\6.1.1\spring-oxm-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.1.1\spring-context-support-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson-spring-boot-starter\3.24.3\redisson-spring-boot-starter-3.24.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.0\spring-boot-starter-actuator-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.0\spring-boot-actuator-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.2.0\spring-boot-actuator-3.2.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.12.0\micrometer-jakarta9-1.12.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.12.0\micrometer-core-1.12.0.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson\3.24.3\redisson-3.24.3.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.101.Final\netty-resolver-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.101.Final\netty-codec-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava3\rxjava\3.1.8\rxjava-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling\2.0.11.Final\jboss-marshalling-2.0.11.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling-river\2.0.11.Final\jboss-marshalling-river-2.0.11.Final.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\kryo\5.5.0\kryo-5.5.0.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\reflectasm\1.11.9\reflectasm-1.11.9.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\minlog\1.3.1\minlog-1.3.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.3\jackson-dataformat-yaml-2.15.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-bean\5.1.6\jodd-bean-5.1.6.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-core\5.1.6\jodd-core-5.1.6.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson-spring-data-31\3.24.3\redisson-spring-data-31-3.24.3.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.34\lombok-1.18.34.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-impl\0.11.5\jjwt-impl-0.11.5.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-jackson\0.11.5\jjwt-jackson-0.11.5.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-ui\2.2.0\springdoc-openapi-starter-webmvc-ui-2.2.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-api\2.2.0\springdoc-openapi-starter-webmvc-api-2.2.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-common\2.2.0\springdoc-openapi-starter-common-2.2.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core-jakarta\2.2.15\swagger-core-jakarta-2.2.15.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations-jakarta\2.2.15\swagger-annotations-jakarta-2.2.15.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models-jakarta\2.2.15\swagger-models-jakarta-2.2.15.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\5.2.0\swagger-ui-5.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.3\asm-9.3.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\Users\<USER>\.m2\repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Taipei"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="TW"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-21\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire9724742186691649477\surefirebooter-20250730112152841_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire9724742186691649477 2025-07-30T11-21-52_586-jvmRun1 surefire-20250730112152841_1tmp surefire_0-20250730112152841_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache\target\test-classes;C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.2.0\spring-boot-starter-web-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.2.0\spring-boot-starter-json-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.3\jackson-datatype-jdk8-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.3\jackson-module-parameter-names-2.15.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.0\spring-boot-starter-tomcat-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-10.1.16.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.16\tomcat-embed-el-10.1.16.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.16\tomcat-embed-websocket-10.1.16.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.1.1\spring-web-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.1.1\spring-webmvc-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;C:\Users\<USER>\.m2\repository\com\nanshan\common-cache\1.0.0-SNAPSHOT\common-cache-1.0.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\3.2.0\spring-boot-starter-data-redis-3.2.0.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.3.0.RELEASE\lettuce-core-6.3.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.6.0\reactor-core-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\3.2.0\spring-data-redis-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\3.2.0\spring-data-keyvalue-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.2.0\spring-data-commons-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.1.1\spring-tx-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\6.1.1\spring-oxm-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.1.1\spring-context-support-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson-spring-boot-starter\3.24.3\redisson-spring-boot-starter-3.24.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.0\spring-boot-starter-actuator-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.0\spring-boot-actuator-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.2.0\spring-boot-actuator-3.2.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.12.0\micrometer-jakarta9-1.12.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.12.0\micrometer-core-1.12.0.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson\3.24.3\redisson-3.24.3.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.101.Final\netty-resolver-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.101.Final\netty-codec-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava3\rxjava\3.1.8\rxjava-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling\2.0.11.Final\jboss-marshalling-2.0.11.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling-river\2.0.11.Final\jboss-marshalling-river-2.0.11.Final.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\kryo\5.5.0\kryo-5.5.0.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\reflectasm\1.11.9\reflectasm-1.11.9.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\minlog\1.3.1\minlog-1.3.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.3\jackson-dataformat-yaml-2.15.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-bean\5.1.6\jodd-bean-5.1.6.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-core\5.1.6\jodd-core-5.1.6.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson-spring-data-31\3.24.3\redisson-spring-data-31-3.24.3.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.34\lombok-1.18.34.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-impl\0.11.5\jjwt-impl-0.11.5.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-jackson\0.11.5\jjwt-jackson-0.11.5.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-ui\2.2.0\springdoc-openapi-starter-webmvc-ui-2.2.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-api\2.2.0\springdoc-openapi-starter-webmvc-api-2.2.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-common\2.2.0\springdoc-openapi-starter-common-2.2.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core-jakarta\2.2.15\swagger-core-jakarta-2.2.15.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations-jakarta\2.2.15\swagger-annotations-jakarta-2.2.15.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models-jakarta\2.2.15\swagger-models-jakarta-2.2.15.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\5.2.0\swagger-ui-5.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.3\asm-9.3.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\Users\<USER>\.m2\repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-21"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire9724742186691649477\surefirebooter-20250730112152841_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.7+8-LTS-245"/>
    <property name="user.name" value="Nofi"/>
    <property name="stdout.encoding" value="MS950"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="30848"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="MS950"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\pleiades.java-extension-pack-jdk\gradle\latest\bin;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\pleiades.java-extension-pack-jdk\maven\latest\bin;C:\Program Files\Java\jdk-21\bin;C:\Python313\Scripts\;C:\Python313\;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;C:\Windows\System32\curl.exe;C:\Windows\System32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\dotnet\;C:\Program Files\Java\jdk-21\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\apache-maven-3.9.10\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.1\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="MS950"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.7+8-LTS-245"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>
    <property name="LOGGED_APPLICATION_NAME" value="[demo-common-cache] "/>
  </properties>
  <testcase name="testCommonCacheRedisConfiguration" classname="com.nanshan.demo.config.RedisConfigurationValidationTest" time="1.354">
    <system-out><![CDATA[11:21:53,955 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-classic version 1.4.11
11:21:53,961 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - Here is a list of configurators discovered as a service, by rank: 
11:21:53,961 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d -   org.springframework.boot.logging.logback.RootLogLevelConfigurator
11:21:53,961 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - They will be invoked in order until ExecutionStatus.DO_NOT_INVOKE_NEXT_IF_ANY is returned.
11:21:53,961 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - Constructed configurator of type class org.springframework.boot.logging.logback.RootLogLevelConfigurator
11:21:53,980 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - org.springframework.boot.logging.logback.RootLogLevelConfigurator.configure() call lasted 1 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
11:21:53,980 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - Trying to configure with ch.qos.logback.classic.joran.SerializedModelConfigurator
11:21:53,982 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - Constructed configurator of type class ch.qos.logback.classic.joran.SerializedModelConfigurator
11:21:54,014 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.scmo]
11:21:54,015 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.scmo]
11:21:54,015 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - ch.qos.logback.classic.joran.SerializedModelConfigurator.configure() call lasted 33 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
11:21:54,015 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - Trying to configure with ch.qos.logback.classic.util.DefaultJoranConfigurator
11:21:54,016 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - Constructed configurator of type class ch.qos.logback.classic.util.DefaultJoranConfigurator
11:21:54,017 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.xml]
11:21:54,017 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.xml]
11:21:54,017 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - ch.qos.logback.classic.util.DefaultJoranConfigurator.configure() call lasted 1 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
11:21:54,018 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - Trying to configure with ch.qos.logback.classic.BasicConfigurator
11:21:54,019 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - Constructed configurator of type class ch.qos.logback.classic.BasicConfigurator
11:21:54,019 |-INFO in ch.qos.logback.classic.BasicConfigurator@322803db - Setting up default configuration.
11:21:54,044 |-INFO in ch.qos.logback.classic.util.ContextInitializer@4b2a30d - ch.qos.logback.classic.BasicConfigurator.configure() call lasted 25 milliseconds. ExecutionStatus=NEUTRAL
11:21:55,143 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [CONSOLE]
11:21:55,143 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
11:21:55,148 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
11:21:55,171 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [FILE]
11:21:55,171 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
11:21:55,175 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
11:21:55,191 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1455064947 - No compression will be used
11:21:55,195 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1455064947 - Will use the pattern logs/demo-common-cache.%d{yyyy-MM-dd}.%i.log for the active file
11:21:55,198 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@6ceb7b5e - The date pattern is 'yyyy-MM-dd' from file name pattern 'logs/demo-common-cache.%d{yyyy-MM-dd}.%i.log'.
11:21:55,198 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@6ceb7b5e - Roll-over at midnight.
11:21:55,200 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@6ceb7b5e - Setting initial period to 2025-07-29T10:11:35.786Z
11:21:55,200 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@6ceb7b5e - SizeAndTimeBasedFNATP is deprecated. Use SizeAndTimeBasedRollingPolicy instead
11:21:55,200 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@6ceb7b5e - For more information see http://logback.qos.ch/manual/appenders.html#SizeAndTimeBasedRollingPolicy
11:21:55,203 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[FILE] - Active log file name: logs/demo-common-cache.log
11:21:55,203 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[FILE] - File property is set to [logs/demo-common-cache.log]
11:21:55,205 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO
11:21:55,205 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating INFO level on Logger[ROOT] onto the JUL framework
11:21:55,206 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[ROOT]
11:21:55,206 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[ROOT]
11:21:55,206 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [com.nanshan] to INFO
11:21:55,206 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating INFO level on Logger[com.nanshan] onto the JUL framework
11:21:55,209 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [com.nanshan] to false
11:21:55,209 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[com.nanshan]
11:21:55,209 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[com.nanshan]
11:21:55,209 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.redisson] to WARN
11:21:55,209 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating WARN level on Logger[org.redisson] onto the JUL framework
11:21:55,209 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [org.redisson] to false
11:21:55,209 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[org.redisson]
11:21:55,209 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[org.redisson]
11:21:55,209 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.redisson.command] to ERROR
11:21:55,209 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating ERROR level on Logger[org.redisson.command] onto the JUL framework
11:21:55,209 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [org.redisson.command] to false
11:21:55,209 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[org.redisson.command]
11:21:55,209 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.redisson.connection] to WARN
11:21:55,209 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating WARN level on Logger[org.redisson.connection] onto the JUL framework
11:21:55,209 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [org.redisson.connection] to false
11:21:55,209 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[org.redisson.connection]
11:21:55,209 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[org.redisson.connection]
11:21:55,209 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.springframework.data.redis] to INFO
11:21:55,209 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating INFO level on Logger[org.springframework.data.redis] onto the JUL framework
11:21:55,209 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [org.springframework.data.redis] to false
11:21:55,209 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[org.springframework.data.redis]
11:21:55,209 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[org.springframework.data.redis]
11:21:55,209 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [io.lettuce.core] to WARN
11:21:55,209 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating WARN level on Logger[io.lettuce.core] onto the JUL framework
11:21:55,210 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [io.lettuce.core] to false
11:21:55,210 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[io.lettuce.core]
11:21:55,210 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[io.lettuce.core]
11:21:55,210 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [io.netty] to WARN
11:21:55,210 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating WARN level on Logger[io.netty] onto the JUL framework
11:21:55,211 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [io.netty] to false
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[io.netty]
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[io.netty]
11:21:55,211 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.springframework.boot] to INFO
11:21:55,211 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating INFO level on Logger[org.springframework.boot] onto the JUL framework
11:21:55,211 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [org.springframework.boot] to false
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[org.springframework.boot]
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[org.springframework.boot]
11:21:55,211 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.springframework.web] to INFO
11:21:55,211 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating INFO level on Logger[org.springframework.web] onto the JUL framework
11:21:55,211 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [org.springframework.web] to false
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[org.springframework.web]
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[org.springframework.web]
11:21:55,211 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [org.springdoc] to INFO
11:21:55,211 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating INFO level on Logger[org.springdoc] onto the JUL framework
11:21:55,211 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [org.springdoc] to false
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[org.springdoc]
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[org.springdoc]
11:21:55,211 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [com.nanshan] to DEBUG
11:21:55,211 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@7dd00705 - Propagating DEBUG level on Logger[com.nanshan] onto the JUL framework
11:21:55,211 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [com.nanshan] to false
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[com.nanshan]
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[com.nanshan]
11:21:55,211 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@f14e5bf - End of configuration.
11:21:55,212 |-INFO in org.springframework.boot.logging.logback.SpringBootJoranConfigurator@d176a31 - Registering current configuration as safe fallback point


  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

11:21:55.307 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.d.c.RedisConfigurationValidationTest&amp#27;[0;39m - Starting RedisConfigurationValidationTest using Java 21.0.7 with PID 30848 (started by Nofi in C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache)
11:21:55.316 [main] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mc.n.d.c.RedisConfigurationValidationTest&amp#27;[0;39m - Running with Spring Boot v3.2.0, Spring v6.1.1
11:21:55.319 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.d.c.RedisConfigurationValidationTest&amp#27;[0;39m - The following 1 profile is active: "dev"
11:21:57.703 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mo.s.d.r.c.RepositoryConfigurationDelegate&amp#27;[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
11:21:57.717 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mo.s.d.r.c.RepositoryConfigurationDelegate&amp#27;[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
11:21:57.782 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mo.s.d.r.c.RepositoryConfigurationDelegate&amp#27;[0;39m - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
Configured Redisson single server: redis://localhost:6379
Created robust ObjectMapper with control character handling
Redisson client configured successfully with enhanced JSON parsing
11:21:58.851 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36morg.redisson.Version&amp#27;[0;39m - Redisson 3.24.3
11:21:59.311 [redisson-netty-2-3] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36morg.redisson.client.RedisConnection&amp#27;[0;39m - Connection created [addr=redis://localhost:6379]
11:21:59.354 [redisson-netty-2-4] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36morg.redisson.client.RedisConnection&amp#27;[0;39m - Connection created [addr=redis://localhost:6379]
11:21:59.377 [redisson-netty-2-7] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36morg.redisson.client.RedisConnection&amp#27;[0;39m - Connection created [addr=redis://localhost:6379]
11:21:59.481 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.c.c.c.CommonCacheAutoConfiguration&amp#27;[0;39m - Configuring RedisDataAccessor
11:21:59.488 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.c.c.c.CommonCacheAutoConfiguration&amp#27;[0;39m - Configuring CacheManager
11:21:59.495 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.c.c.c.CommonCacheAutoConfiguration&amp#27;[0;39m - Configuring SessionManager
11:21:59.576 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.c.c.s.impl.SessionManagerImpl&amp#27;[0;39m - SessionManager Redisson optimizations initialized successfully
11:21:59.580 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.c.c.c.CommonCacheAutoConfiguration&amp#27;[0;39m - Configuring RedisCleaner
11:21:59.903 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.demo.cache.service.AuthService&amp#27;[0;39m - 初始化了 3 個示範用戶
11:21:59.920 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.c.c.c.CommonCacheAutoConfiguration&amp#27;[0;39m - Configuring LockManager
11:21:59.979 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.c.c.c.CommonCacheAutoConfiguration&amp#27;[0;39m - Configuring RedisConnectionManager
11:22:00.013 [redisson-netty-2-12] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36morg.redisson.client.RedisConnection&amp#27;[0;39m - Connection created [addr=redis://localhost:6379]
11:22:00.035 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.c.c.s.RedisConnectionManager&amp#27;[0;39m - Redis connection test successful
11:22:00.038 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.c.c.s.RedisConnectionManager&amp#27;[0;39m - Redis connection manager initialized successfully
11:22:00.796 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mo.s.b.a.e.web.EndpointLinksResolver&amp#27;[0;39m - Exposing 4 endpoint(s) beneath base path '/actuator'
11:22:02.030 [main] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mi.l.c.r.AddressResolverGroupProvider&amp#27;[0;39m - Starting with netty's non-blocking DNS resolver library
11:22:02.038 [main] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mi.l.core.resource.KqueueProvider&amp#27;[0;39m - Starting without optional kqueue library
11:22:02.043 [main] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mi.l.core.resource.IOUringProvider&amp#27;[0;39m - Starting without optional io_uring library
11:22:02.045 [main] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mi.l.core.resource.EpollProvider&amp#27;[0;39m - Starting without optional epoll library
11:22:02.045 [main] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mi.l.c.r.DefaultClientResources&amp#27;[0;39m - -Dio.netty.eventLoopThreads: 16
11:22:02.070 [main] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mi.l.c.r.DefaultEventLoopGroupProvider&amp#27;[0;39m - Creating executor io.netty.util.concurrent.DefaultEventExecutorGroup
11:22:02.108 [main] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mi.l.c.event.jfr.EventRecorderHolder&amp#27;[0;39m - Starting with JFR support
11:22:02.365 [main] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mo.s.d.r.l.RedisMessageListenerContainer&amp#27;[0;39m - Starting RedisMessageListenerContainer...
11:22:02.367 [main] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mo.s.d.r.l.RedisMessageListenerContainer&amp#27;[0;39m - Postpone listening for Redis messages until actual listeners are added
11:22:02.490 [main] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.d.c.RedisConfigurationValidationTest&amp#27;[0;39m - Started RedisConfigurationValidationTest in 7.96 seconds (process running for 9.435)
11:22:02.540 [scheduling-1] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mc.n.c.c.s.impl.RedisCleanerImpl&amp#27;[0;39m - Starting scheduled cleanup...
11:22:02.572 [scheduling-1] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mc.n.c.c.s.impl.SessionManagerImpl&amp#27;[0;39m - Cleaned up 0 expired sessions
11:22:02.573 [scheduling-1] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mc.n.c.c.s.impl.RedisCleanerImpl&amp#27;[0;39m - Cleaned 0 expired sessions
11:22:02.575 [scheduling-1] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mc.n.c.c.s.impl.CacheManagerImpl&amp#27;[0;39m - Cleaned up 0 expired caches
11:22:02.575 [scheduling-1] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mc.n.c.c.s.impl.RedisCleanerImpl&amp#27;[0;39m - Cleaned 0 expired caches
11:22:02.577 [scheduling-1] &amp#27;[39mDEBUG&amp#27;[0;39m &amp#27;[36mc.n.c.c.s.impl.RedisCleanerImpl&amp#27;[0;39m - Cleaned 0 expired locks
11:22:02.585 [scheduling-1] &amp#27;[34mINFO &amp#27;[0;39m &amp#27;[36mc.n.c.c.s.impl.RedisCleanerImpl&amp#27;[0;39m - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=37ms
]]></system-out>
    <system-err><![CDATA[WARNING: A Java agent has been loaded dynamically (C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
  </testcase>
  <testcase name="testDefaultRedisConfiguration" classname="com.nanshan.demo.config.RedisConfigurationValidationTest" time="0.017"/>
  <testcase name="testCommonCacheOtherConfiguration" classname="com.nanshan.demo.config.RedisConfigurationValidationTest" time="0.006"/>
</testsuite>