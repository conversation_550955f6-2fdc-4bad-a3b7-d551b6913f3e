2025-07-25 13:51:19.789 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 13:51:19.799 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 13:51:19.799 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 13:51:19.800 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 13:51:19.800 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 13:51:19.808 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 13:51:19.808 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-25 13:55:01.147 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-25 13:56:19.824 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 13:56:19.826 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 13:56:19.826 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 13:56:19.827 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 13:56:19.827 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 13:56:19.827 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 13:56:19.829 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 14:01:19.840 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:01:19.842 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:01:19.842 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:01:19.843 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:01:19.843 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:01:19.843 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:01:19.843 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 14:06:19.855 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:06:19.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:06:19.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:06:19.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:06:19.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:06:19.858 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:06:19.858 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 14:11:19.865 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:11:19.867 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:11:19.867 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:11:19.867 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:11:19.867 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:11:19.868 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:11:19.868 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 14:16:19.884 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:16:19.886 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:16:19.886 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:16:19.887 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:16:19.887 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:16:19.887 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:16:19.887 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 14:21:19.893 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:21:19.895 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:21:19.895 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:21:19.895 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:21:19.895 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:21:19.895 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:21:19.895 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 14:25:01.155 [redisson-netty-2-11] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-25 14:26:19.899 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:26:19.901 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:26:19.901 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:26:19.902 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:26:19.902 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:26:19.903 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:26:19.903 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 14:31:19.907 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:31:19.908 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:31:19.908 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:31:19.908 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:31:19.908 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:31:19.910 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:31:19.910 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 14:36:19.913 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:36:19.914 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:36:19.914 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:36:19.915 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:36:19.916 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:36:19.916 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:36:19.916 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 14:41:19.919 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:41:19.921 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:41:19.921 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:41:19.923 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:41:19.923 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:41:19.924 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:41:19.924 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-25 14:46:19.928 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:46:19.930 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:46:19.930 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:46:19.931 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:46:19.931 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:46:19.931 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:46:19.931 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 14:51:19.944 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:51:19.946 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:51:19.946 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:51:19.946 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:51:19.946 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:51:19.947 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:51:19.947 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 14:55:01.168 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-25 14:56:19.961 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 14:56:19.962 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 14:56:19.962 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 14:56:19.963 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 14:56:19.963 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 14:56:19.963 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 14:56:19.963 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 15:01:19.964 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:01:19.966 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:01:19.966 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:01:19.967 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:01:19.967 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:01:19.968 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:01:19.969 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=5ms
2025-07-25 15:06:19.978 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:06:19.979 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:06:19.979 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:06:19.980 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:06:19.980 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:06:19.981 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:06:19.981 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 15:11:19.984 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:11:19.985 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:11:19.985 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:11:19.985 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:11:19.985 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:11:19.986 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:11:19.986 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 15:16:20.000 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:16:20.001 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:16:20.001 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:16:20.002 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:16:20.002 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:16:20.002 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:16:20.002 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 15:21:20.007 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:21:20.008 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:21:20.008 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:21:20.008 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:21:20.008 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:21:20.008 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:21:20.009 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=1ms
2025-07-25 15:25:01.173 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-25 15:26:20.025 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:26:20.027 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:26:20.027 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:26:20.027 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:26:20.028 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:26:20.028 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:26:20.028 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 15:31:20.032 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:31:20.034 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:31:20.034 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:31:20.034 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:31:20.034 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:31:20.035 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:31:20.035 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 15:36:20.041 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:36:20.042 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:36:20.042 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:36:20.042 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:36:20.042 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:36:20.042 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:36:20.042 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=1ms
2025-07-25 15:41:20.057 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:41:20.059 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:41:20.059 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:41:20.059 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:41:20.059 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:41:20.060 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:41:20.060 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 15:46:20.068 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:46:20.070 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:46:20.070 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:46:20.070 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:46:20.070 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:46:20.071 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:46:20.071 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 15:51:20.078 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:51:20.081 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:51:20.081 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:51:20.081 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:51:20.081 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:51:20.081 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:51:20.081 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=1ms
2025-07-25 15:55:01.185 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-25 15:56:20.084 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 15:56:20.085 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 15:56:20.085 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 15:56:20.087 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 15:56:20.087 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 15:56:20.087 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 15:56:20.087 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 16:01:20.093 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:01:20.094 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:01:20.094 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:01:20.095 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:01:20.095 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:01:20.096 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:01:20.096 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 16:06:20.110 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:06:20.112 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:06:20.112 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:06:20.112 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:06:20.112 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:06:20.112 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:06:20.112 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 16:11:20.126 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:11:20.127 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:11:20.127 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:11:20.127 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:11:20.128 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:11:20.128 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:11:20.128 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 16:16:20.135 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:16:20.136 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:16:20.136 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:16:20.137 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:16:20.137 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:16:20.137 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:16:20.137 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 16:21:20.146 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:21:20.147 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:21:20.147 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:21:20.149 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:21:20.149 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:21:20.149 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:21:20.150 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-25 16:25:01.193 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-25 16:26:20.159 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:26:20.160 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:26:20.161 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:26:20.161 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:26:20.161 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:26:20.161 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:26:20.162 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 16:31:20.173 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:31:20.175 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:31:20.175 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:31:20.176 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:31:20.176 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:31:20.176 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:31:20.176 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 16:36:20.191 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:36:20.193 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:36:20.193 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:36:20.193 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:36:20.193 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:36:20.193 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:36:20.194 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 16:41:20.195 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:41:20.196 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:41:20.196 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:41:20.196 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:41:20.196 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:41:20.197 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:41:20.197 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 16:46:20.198 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:46:20.199 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:46:20.199 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:46:20.200 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:46:20.200 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:46:20.200 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:46:20.200 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 16:51:20.217 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:51:20.218 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:51:20.219 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:51:20.219 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:51:20.220 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:51:20.220 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:51:20.220 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 16:55:01.200 [redisson-netty-2-11] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-25 16:56:20.225 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 16:56:20.227 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 16:56:20.227 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 16:56:20.227 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 16:56:20.227 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 16:56:20.228 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 16:56:20.228 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 17:01:20.241 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:01:20.242 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:01:20.242 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:01:20.242 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:01:20.242 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:01:20.243 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:01:20.243 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 17:06:20.254 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:06:20.255 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:06:20.255 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:06:20.256 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:06:20.256 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:06:20.256 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:06:20.256 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 17:11:20.267 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:11:20.267 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:11:20.267 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:11:20.269 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:11:20.269 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:11:20.269 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:11:20.269 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 17:16:20.283 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:16:20.284 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:16:20.284 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:16:20.285 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:16:20.285 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:16:20.286 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:16:20.286 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 17:21:20.301 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:21:20.302 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:21:20.302 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:21:20.303 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:21:20.303 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:21:20.304 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:21:20.304 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 17:25:01.217 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-25 17:26:20.307 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:26:20.308 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:26:20.308 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:26:20.309 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:26:20.309 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:26:20.309 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:26:20.309 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 17:31:20.315 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:31:20.317 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:31:20.317 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:31:20.317 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:31:20.317 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:31:20.318 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:31:20.320 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 17:36:20.327 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:36:20.329 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:36:20.329 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:36:20.329 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:36:20.329 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:36:20.330 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:36:20.330 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 17:41:20.342 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:41:20.343 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:41:20.343 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:41:20.345 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:41:20.345 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:41:20.346 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:41:20.346 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-25 17:46:20.352 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:46:20.353 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:46:20.353 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:46:20.353 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:46:20.354 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:46:20.354 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:46:20.354 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 17:51:20.371 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:51:20.372 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:51:20.373 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:51:20.373 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:51:20.373 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:51:20.374 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:51:20.374 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-25 17:55:01.229 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-25 17:56:20.380 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 17:56:20.382 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 17:56:20.382 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 17:56:20.383 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 17:56:20.383 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 17:56:20.383 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 17:56:20.383 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 18:01:20.390 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 18:01:20.391 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 18:01:20.391 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 18:01:20.392 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 18:01:20.392 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 18:01:20.392 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 18:01:20.392 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 18:06:20.403 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 18:06:20.404 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 18:06:20.404 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 18:06:20.404 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 18:06:20.404 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 18:06:20.405 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 18:06:20.405 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 18:11:20.413 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-25 18:11:20.414 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-25 18:11:20.414 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-25 18:11:20.415 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-25 18:11:20.415 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-25 18:11:20.415 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-25 18:11:20.415 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-25 23:10:54.990 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
