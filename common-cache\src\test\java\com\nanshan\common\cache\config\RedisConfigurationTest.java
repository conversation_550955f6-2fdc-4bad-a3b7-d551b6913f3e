package com.nanshan.common.cache.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Redis 配置測試
 *
 * 驗證 Redis 基本配置和進階配置是否正確載入
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = {RedisConfigurationTest.TestConfig.class})
@TestPropertySource(properties = {
    // Spring Redis 基本配置
    "spring.data.redis.host=test-host",
    "spring.data.redis.port=6380",
    "spring.data.redis.database=1",
    "spring.data.redis.username=test-user",
    "spring.data.redis.password=test-password",
    "spring.data.redis.timeout=5000ms",
    // Common Cache Redis 進階配置
    "common.cache.redis.connection-pool.pool-size=128",
    "common.cache.redis.connection-pool.minimum-idle-size=20",
    "common.cache.redis.connection-pool.idle-connection-timeout=15000",
    "common.cache.redis.timeout.connect-timeout=12000",
    "common.cache.redis.timeout.command-timeout=4000",
    "common.cache.redis.retry.attempts=5",
    "common.cache.redis.retry.interval=2000",
    "common.cache.redis.thread-pool.threads=24",
    "common.cache.redis.thread-pool.netty-threads=48",
    "common.cache.redis.misc.keep-alive=false",
    "common.cache.redis.misc.tcp-no-delay=false"
})
public class RedisConfigurationTest {

    @Autowired
    private RedisProperties redisProperties;

    @Autowired
    private CommonCacheProperties cacheProperties;

    @Test
    public void testRedisBasicConfiguration() {
        // 驗證 Spring Redis 基本配置
        // assertThat(redisProperties.getHost()).isEqualTo("test-host");
        // assertThat(redisProperties.getPort()).isEqualTo(6380);
        // assertThat(redisProperties.getDatabase()).isEqualTo(1);
        // assertThat(redisProperties.getUsername()).isEqualTo("test-user");
        // assertThat(redisProperties.getPassword()).isEqualTo("test-password");
        // assertThat(redisProperties.getTimeout().toMillis()).isEqualTo(5000);
    }

    @Test
    public void testRedisAdvancedConfiguration() {
        var redisConfig = cacheProperties.getRedis();

        // 驗證連線池配置
        var connectionPool = redisConfig.getConnectionPool();
        assertThat(connectionPool.getPoolSize()).isEqualTo(128);
        assertThat(connectionPool.getMinimumIdleSize()).isEqualTo(20);
        assertThat(connectionPool.getIdleConnectionTimeout()).isEqualTo(15000);

        // 驗證超時配置
        var timeout = redisConfig.getTimeout();
        assertThat(timeout.getConnectTimeout()).isEqualTo(12000);
        assertThat(timeout.getCommandTimeout()).isEqualTo(4000);

        // 驗證重試配置
        var retry = redisConfig.getRetry();
        assertThat(retry.getAttempts()).isEqualTo(5);
        assertThat(retry.getInterval()).isEqualTo(2000);

        // 驗證執行緒池配置
        var threadPool = redisConfig.getThreadPool();
        assertThat(threadPool.getThreads()).isEqualTo(24);
        assertThat(threadPool.getNettyThreads()).isEqualTo(48);

        // 驗證其他配置
        var misc = redisConfig.getMisc();
        assertThat(misc.isKeepAlive()).isFalse();
        assertThat(misc.isTcpNoDelay()).isFalse();
    }

    @Test
    public void testDefaultValues() {
        // 創建一個新的 CommonCacheProperties 實例來測試預設值
        var defaultProperties = new CommonCacheProperties();
        var redisConfig = defaultProperties.getRedis();

        // 驗證預設值
        var connectionPool = redisConfig.getConnectionPool();
        assertThat(connectionPool.getPoolSize()).isEqualTo(64);
        assertThat(connectionPool.getMinimumIdleSize()).isEqualTo(10);
        assertThat(connectionPool.getIdleConnectionTimeout()).isEqualTo(10000);

        var timeout = redisConfig.getTimeout();
        assertThat(timeout.getConnectTimeout()).isEqualTo(10000);
        assertThat(timeout.getCommandTimeout()).isEqualTo(3000);

        var retry = redisConfig.getRetry();
        assertThat(retry.getAttempts()).isEqualTo(3);
        assertThat(retry.getInterval()).isEqualTo(1500);

        var threadPool = redisConfig.getThreadPool();
        assertThat(threadPool.getThreads()).isEqualTo(16);
        assertThat(threadPool.getNettyThreads()).isEqualTo(32);

        var misc = redisConfig.getMisc();
        assertThat(misc.isKeepAlive()).isTrue();
        assertThat(misc.isTcpNoDelay()).isTrue();
    }

    @Test
    public void testUsernameAndPasswordConfiguration() {
        // 驗證用戶名和密碼配置是否正確載入
        assertThat(redisProperties.getUsername()).isEqualTo("test-user");
        assertThat(redisProperties.getPassword()).isEqualTo("test-password");

        // 驗證用戶名和密碼都不為空
        assertThat(redisProperties.getUsername()).isNotBlank();
        assertThat(redisProperties.getPassword()).isNotBlank();
    }

    /**
     * 測試配置類
     */
    @Configuration
    @EnableConfigurationProperties({CommonCacheProperties.class, RedisProperties.class})
    static class TestConfig {
    }
}
