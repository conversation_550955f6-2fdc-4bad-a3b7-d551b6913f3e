@echo off
echo "=== Redis 數據清理腳本 ==="
echo.
echo "此腳本將清理 Redis 中可能包含控制字符的損壞數據"
echo "請確保 Redis 服務正在運行"
echo.

set /p confirm="確定要清理 Redis 數據嗎？(y/N): "
if /i not "%confirm%"=="y" (
    echo "操作已取消"
    pause
    exit /b 0
)

echo.
echo "正在連接到 Redis..."

:: 清理所有 common-cache 相關的 key
redis-cli --scan --pattern "common-cache:*" | redis-cli -x del

:: 清理可能損壞的 session 數據
redis-cli --scan --pattern "session:*" | redis-cli -x del

:: 清理可能損壞的 cache 數據
redis-cli --scan --pattern "cache:*" | redis-cli -x del

:: 清理可能損壞的 lock 數據
redis-cli --scan --pattern "lock:*" | redis-cli -x del

echo.
echo "Redis 數據清理完成！"
echo "現在可以重新啟動應用程序"
echo.
pause
