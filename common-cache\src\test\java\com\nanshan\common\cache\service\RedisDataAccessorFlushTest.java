package com.nanshan.common.cache.service;

import com.nanshan.common.cache.annotation.EnableRedisSupport;
import com.nanshan.common.cache.config.TestConfig;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Redis Flush 操作測試
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = RedisDataAccessorFlushTest.FlushTestApplication.class)
public class RedisDataAccessorFlushTest {

    @SpringBootApplication
    @EnableRedisSupport
    @Import(TestConfig.class)
    public static class FlushTestApplication {
        public static void main(String[] args) {
            SpringApplication.run(FlushTestApplication.class, args);
        }
    }

    @Autowired
    private RedisDataAccessor redisDataAccessor;

    @BeforeEach
    void setUp() {
        // 清理測試環境
        redisDataAccessor.setString("test:key1", "value1");
        redisDataAccessor.setString("test:key2", "value2");
        redisDataAccessor.setString("test:key3", "value3");
    }

    @AfterEach
    void tearDown() {
        // 清理測試資料
        redisDataAccessor.delete("test:key1", "test:key2", "test:key3");
    }

    /**
     * 測試在開發環境中 flushDatabase 操作
     */
    @Test
    void testFlushDatabaseInDevEnvironment() {
        // 驗證測試資料存在
        assertThat(redisDataAccessor.exists("test:key1")).isTrue();
        assertThat(redisDataAccessor.exists("test:key2")).isTrue();
        assertThat(redisDataAccessor.exists("test:key3")).isTrue();

        // 執行 flushDatabase
        boolean result = redisDataAccessor.flushDatabase();

        // 驗證操作成功
        assertThat(result).isTrue();

        // 驗證所有鍵都被刪除
        assertThat(redisDataAccessor.exists("test:key1")).isFalse();
        assertThat(redisDataAccessor.exists("test:key2")).isFalse();
        assertThat(redisDataAccessor.exists("test:key3")).isFalse();
    }

    /**
     * 測試在開發環境中 flushAllDatabases 操作
     */
    @Test
    void testFlushAllDatabasesInDevEnvironment() {
        // 驗證測試資料存在
        assertThat(redisDataAccessor.exists("test:key1")).isTrue();
        assertThat(redisDataAccessor.exists("test:key2")).isTrue();
        assertThat(redisDataAccessor.exists("test:key3")).isTrue();

        // 執行 flushAllDatabases
        boolean result = redisDataAccessor.flushAllDatabases();

        // 驗證操作成功
        assertThat(result).isTrue();

        // 驗證所有鍵都被刪除
        assertThat(redisDataAccessor.exists("test:key1")).isFalse();
        assertThat(redisDataAccessor.exists("test:key2")).isFalse();
        assertThat(redisDataAccessor.exists("test:key3")).isFalse();
    }

    /**
     * 測試在生產環境中 flushDatabase 操作被阻止
     * 注意：由於測試環境預設不是生產環境，這個測試實際上會成功執行操作
     * 真正的生產環境保護需要在實際部署時透過 Profile 配置
     */
    @Test
    void testFlushDatabaseInTestEnvironment() {
        // 驗證測試資料存在
        assertThat(redisDataAccessor.exists("test:key1")).isTrue();
        assertThat(redisDataAccessor.exists("test:key2")).isTrue();
        assertThat(redisDataAccessor.exists("test:key3")).isTrue();

        // 執行 flushDatabase（在測試環境中應該成功）
        boolean result = redisDataAccessor.flushDatabase();

        // 驗證操作成功
        assertThat(result).isTrue();

        // 驗證所有鍵都被刪除
        assertThat(redisDataAccessor.exists("test:key1")).isFalse();
        assertThat(redisDataAccessor.exists("test:key2")).isFalse();
        assertThat(redisDataAccessor.exists("test:key3")).isFalse();
    }

    /**
     * 測試在生產環境中 flushAllDatabases 操作被阻止
     * 注意：由於測試環境預設不是生產環境，這個測試實際上會成功執行操作
     * 真正的生產環境保護需要在實際部署時透過 Profile 配置
     */
    @Test
    void testFlushAllDatabasesInTestEnvironment() {
        // 驗證測試資料存在
        assertThat(redisDataAccessor.exists("test:key1")).isTrue();
        assertThat(redisDataAccessor.exists("test:key2")).isTrue();
        assertThat(redisDataAccessor.exists("test:key3")).isTrue();

        // 執行 flushAllDatabases（在測試環境中應該成功）
        boolean result = redisDataAccessor.flushAllDatabases();

        // 驗證操作成功
        assertThat(result).isTrue();

        // 驗證所有鍵都被刪除
        assertThat(redisDataAccessor.exists("test:key1")).isFalse();
        assertThat(redisDataAccessor.exists("test:key2")).isFalse();
        assertThat(redisDataAccessor.exists("test:key3")).isFalse();
    }

    /**
     * 內部測試類 - 生產環境但允許危險操作
     */
    @SpringBootTest(
        classes = RedisDataAccessorFlushTest.FlushTestApplication.class,
        properties = {
            "common.cache.env-guard.dangerous-operations-enabled=true"
        }
    )
    static class ProdEnvironmentWithDangerousOperationsEnabledTest {

        @Autowired
        private RedisDataAccessor redisDataAccessor;

        @BeforeEach
        void setUp() {
            redisDataAccessor.setString("test:key1", "value1");
            redisDataAccessor.setString("test:key2", "value2");
        }

        @AfterEach
        void tearDown() {
            redisDataAccessor.delete("test:key1", "test:key2");
        }

        /**
         * 測試在生產環境中啟用危險操作時 flushDatabase 可以執行
         */
        @Test
        void testFlushDatabaseAllowedWhenDangerousOperationsEnabled() {
            // 驗證測試資料存在
            assertThat(redisDataAccessor.exists("test:key1")).isTrue();
            assertThat(redisDataAccessor.exists("test:key2")).isTrue();

            // 執行 flushDatabase（應該被允許）
            boolean result = redisDataAccessor.flushDatabase();

            // 驗證操作成功
            assertThat(result).isTrue();

            // 驗證所有鍵都被刪除
            assertThat(redisDataAccessor.exists("test:key1")).isFalse();
            assertThat(redisDataAccessor.exists("test:key2")).isFalse();
        }

        /**
         * 測試在生產環境中啟用危險操作時 flushAllDatabases 可以執行
         */
        @Test
        void testFlushAllDatabasesAllowedWhenDangerousOperationsEnabled() {
            // 驗證測試資料存在
            assertThat(redisDataAccessor.exists("test:key1")).isTrue();
            assertThat(redisDataAccessor.exists("test:key2")).isTrue();

            // 執行 flushAllDatabases（應該被允許）
            boolean result = redisDataAccessor.flushAllDatabases();

            // 驗證操作成功
            assertThat(result).isTrue();

            // 驗證所有鍵都被刪除
            assertThat(redisDataAccessor.exists("test:key1")).isFalse();
            assertThat(redisDataAccessor.exists("test:key2")).isFalse();
        }
    }
}
