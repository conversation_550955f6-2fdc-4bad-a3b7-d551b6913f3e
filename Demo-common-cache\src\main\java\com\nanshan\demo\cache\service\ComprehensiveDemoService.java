package com.nanshan.demo.cache.service;

import com.nanshan.common.cache.model.SessionObject;
import com.nanshan.common.cache.model.SessionObjectBuilder;
import com.nanshan.common.cache.service.CacheManager;
import com.nanshan.common.cache.service.LockManager;
import com.nanshan.common.cache.service.RedisCleaner;
import com.nanshan.common.cache.service.SessionManager;
import com.nanshan.demo.cache.model.Product;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 綜合示範服務
 *
 * 整合所有功能模組的完整使用流程示範
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ComprehensiveDemoService {

    private final SessionManager sessionManager;
    private final CacheManager cacheManager;
    private final LockManager lockManager;
    private final RedisCleaner redisCleaner;

    /**
     * 執行綜合示範
     *
     * @return 示範結果
     */
    public Map<String, Object> runComprehensiveDemo() {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> steps = new HashMap<>();

        try {
            log.info("開始執行綜合示範");

            // 步驟 1: 建立使用者 Session
            String userId = "user-" + System.currentTimeMillis();
            String jwtId = UUID.randomUUID().toString();

            SessionObject session = SessionObjectBuilder.createFullSession(
                    userId,
                    "demo-app",
                    jwtId,
                    Arrays.asList("USER", "ADMIN"),
                    Arrays.asList("READ", "WRITE", "DELETE"),
                    "127.0.0.1",
                    "Demo Device",
                    "Mozilla/5.0 Demo Browser",
                    1800 // TTL 秒數
            );

            sessionManager.saveSession(jwtId, session, 1800);
            log.info("步驟 1: 建立使用者 Session 完成, JWT ID: {}", jwtId);

            steps.put("step1_create_session", Map.of(
                    "success", true,
                    "jwtId", jwtId,
                    "userId", userId,
                    "ttl", -1 // 暫時設為 -1
            ));

            // 步驟 2: 使用分散式鎖保護產品建立
            String productId = "PROD-" + System.currentTimeMillis();
            Product product;
            try {
                lockManager.lock("demo:product:create", 20, TimeUnit.SECONDS);
                // 在鎖保護下執行
                log.info("步驟 2: 在鎖保護下建立產品");

                // 模擬產品建立邏輯
                Product newProduct = Product.builder()
                        .productId(productId)
                        .name("綜合示範產品")
                        .description("用於綜合示範的測試產品")
                        .price(new BigDecimal("199.99"))
                        .category("綜合測試")
                        .tags(Arrays.asList("綜合", "示範", "測試"))
                        .stock(100)
                        .enabled(true)
                        .createdAt(LocalDateTime.now())
                        .updatedAt(LocalDateTime.now())
                        .supplier("綜合示範供應商")
                        .specifications("綜合示範規格")
                        .build();

                product = newProduct;
            } finally {
                lockManager.unlock("demo:product:create");
            }

            log.info("步驟 2: 使用分散式鎖保護產品建立完成, 產品 ID: {}", productId);

            steps.put("step2_create_product_with_lock", Map.of(
                    "success", true,
                    "productId", productId,
                    "productName", product.getName()
            ));

            // 步驟 3: 將產品資訊儲存到快取
            cacheManager.put("product", productId, product, 3600, TimeUnit.SECONDS);
            log.info("步驟 3: 將產品資訊儲存到快取完成");

            steps.put("step3_cache_product", Map.of(
                    "success", true,
                    "productId", productId,
                    "ttl", cacheManager.getRemainingTimeToLive("product", productId, TimeUnit.SECONDS)
            ));

            // 步驟 4: 模擬使用者瀏覽產品 (讀取快取)
            Optional<Product> cachedProduct = cacheManager.get("product", productId, Product.class);
            log.info("步驟 4: 模擬使用者瀏覽產品 (讀取快取) 完成, 快取命中: {}", cachedProduct.isPresent());

            steps.put("step4_browse_product", Map.of(
                    "success", true,
                    "cacheHit", cachedProduct.isPresent(),
                    "productName", cachedProduct.map(Product::getName).orElse(null)
            ));

            // 步驟 5: 使用分散式鎖更新產品庫存
            boolean stockUpdateSuccess = lockManager.tryWriteLock("demo:product:stock:" + productId, 5, 15, TimeUnit.SECONDS);

            if (stockUpdateSuccess) {
                try {
                    log.info("步驟 5: 獲取寫鎖更新產品庫存");

                    // 從快取獲取最新產品資訊
                    Optional<Product> productToUpdate = cacheManager.get("product", productId, Product.class);

                    if (productToUpdate.isPresent()) {
                        Product updatedProduct = productToUpdate.get();
                        // 更新庫存
                        updatedProduct.setStock(updatedProduct.getStock() - 10);
                        updatedProduct.setUpdatedAt(LocalDateTime.now());

                        // 更新快取
                        cacheManager.put("product", productId, updatedProduct);

                        steps.put("step5_update_stock", Map.of(
                                "success", true,
                                "newStock", updatedProduct.getStock(),
                                "updateTime", updatedProduct.getUpdatedAt()
                        ));
                    } else {
                        steps.put("step5_update_stock", Map.of(
                                "success", false,
                                "message", "產品不存在於快取中"
                        ));
                    }

                } finally {
                    lockManager.unlock("demo:product:stock:" + productId);
                    log.info("步驟 5: 釋放寫鎖");
                }
            } else {
                steps.put("step5_update_stock", Map.of(
                        "success", false,
                        "message", "無法獲取寫鎖"
                ));
            }

            // 步驟 6: 更新 Session 最後活動時間
            boolean sessionUpdated = sessionManager.updateLastActiveTime(jwtId);
            log.info("步驟 6: 更新 Session 最後活動時間完成, 結果: {}", sessionUpdated);

            steps.put("step6_update_session", Map.of(
                    "success", sessionUpdated,
                    "newTtl", -1 // 暫時設為 -1
            ));

            // 步驟 7: 執行清理操作
            RedisCleaner.CleanupResult cleanupResult = redisCleaner.performManualCleanup();
            log.info("步驟 7: 執行清理操作完成, 清理結果: {}", cleanupResult.isSuccess());

            steps.put("step7_cleanup", Map.of(
                    "success", cleanupResult.isSuccess(),
                    "sessionsCleaned", cleanupResult.getSessionsCleaned(),
                    "cachesCleaned", cleanupResult.getCachesCleaned(),
                    "locksCleaned", cleanupResult.getLocksCleaned(),
                    "durationMs", cleanupResult.getDurationMs()
            ));

            // 步驟 8: 獲取統計資訊
            SessionManager.SessionStats sessionStats = sessionManager.getSessionStats();
            CacheManager.CacheStats cacheStats = cacheManager.getStats();

            log.info("步驟 8: 獲取統計資訊完成");

            steps.put("step8_stats", Map.of(
                    "success", true,
                    "sessionStats", Map.of(
                            "totalSessions", sessionStats.getTotalSessions(),
                            "activeSessions", sessionStats.getActiveSessions()
                    ),
                    "cacheStats", Map.of(
                            "totalRequests", cacheStats.getTotalRequests(),
                            "hitRate", cacheStats.getHitRate()
                    )
            ));

            // 步驟 9: 清理示範資源
            sessionManager.deleteSession(jwtId);
            cacheManager.remove("product", productId);

            log.info("步驟 9: 清理示範資源完成");

            steps.put("step9_cleanup_resources", Map.of(
                    "success", true,
                    "sessionDeleted", !sessionManager.existsSession(jwtId),
                    "cacheDeleted", !cacheManager.exists("product", productId)
            ));

            // 返回最終結果
            result.put("success", true);
            result.put("message", "綜合示範執行完成");
            result.put("description", "展示了 Session 管理、快取管理、分散式鎖和清理器的完整使用流程");
            result.put("steps", steps);

            log.info("綜合示範執行完成");

        } catch (Exception e) {
            log.error("綜合示範執行失敗", e);

            result.put("success", false);
            result.put("message", "綜合示範執行失敗: " + e.getMessage());
            result.put("steps", steps);
        }

        return result;
    }
}
