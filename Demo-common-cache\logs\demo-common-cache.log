2025-07-30 11:21:55.307 [main] INFO  c.n.demo.config.RedisConfigurationValidationTest - Starting RedisConfigurationValidationTest using Java 21.0.7 with PID 30848 (started by <PERSON><PERSON> in C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache)
2025-07-30 11:21:55.316 [main] DEBUG c.n.demo.config.RedisConfigurationValidationTest - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-07-30 11:21:55.319 [main] INFO  c.n.demo.config.RedisConfigurationValidationTest - The following 1 profile is active: "dev"
2025-07-30 11:21:57.703 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 11:21:57.717 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 11:21:57.782 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-07-30 11:21:58.851 [main] INFO  org.redisson.Version - Redisson 3.24.3
2025-07-30 11:21:59.311 [redisson-netty-2-3] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:21:59.354 [redisson-netty-2-4] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:21:59.377 [redisson-netty-2-7] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:21:59.481 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisDataAccessor
2025-07-30 11:21:59.488 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring CacheManager
2025-07-30 11:21:59.495 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring SessionManager
2025-07-30 11:21:59.576 [main] INFO  c.n.common.cache.service.impl.SessionManagerImpl - SessionManager Redisson optimizations initialized successfully
2025-07-30 11:21:59.580 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisCleaner
2025-07-30 11:21:59.903 [main] INFO  com.nanshan.demo.cache.service.AuthService - 初始化了 3 個示範用戶
2025-07-30 11:21:59.920 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring LockManager
2025-07-30 11:21:59.979 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisConnectionManager
2025-07-30 11:22:00.013 [redisson-netty-2-12] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:22:00.035 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection test successful
2025-07-30 11:22:00.038 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection manager initialized successfully
2025-07-30 11:22:00.796 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-30 11:22:02.030 [main] DEBUG i.l.core.resource.AddressResolverGroupProvider - Starting with netty's non-blocking DNS resolver library
2025-07-30 11:22:02.038 [main] DEBUG io.lettuce.core.resource.KqueueProvider - Starting without optional kqueue library
2025-07-30 11:22:02.043 [main] DEBUG io.lettuce.core.resource.IOUringProvider - Starting without optional io_uring library
2025-07-30 11:22:02.045 [main] DEBUG io.lettuce.core.resource.EpollProvider - Starting without optional epoll library
2025-07-30 11:22:02.045 [main] DEBUG io.lettuce.core.resource.DefaultClientResources - -Dio.netty.eventLoopThreads: 16
2025-07-30 11:22:02.070 [main] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Creating executor io.netty.util.concurrent.DefaultEventExecutorGroup
2025-07-30 11:22:02.108 [main] DEBUG io.lettuce.core.event.jfr.EventRecorderHolder - Starting with JFR support
2025-07-30 11:22:02.365 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Starting RedisMessageListenerContainer...
2025-07-30 11:22:02.367 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Postpone listening for Redis messages until actual listeners are added
2025-07-30 11:22:02.490 [main] INFO  c.n.demo.config.RedisConfigurationValidationTest - Started RedisConfigurationValidationTest in 7.96 seconds (process running for 9.435)
2025-07-30 11:22:02.540 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 11:22:02.572 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 11:22:02.573 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 11:22:02.575 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 11:22:02.575 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 11:22:02.577 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 11:22:02.585 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=37ms
2025-07-30 11:22:04.217 [SpringApplicationShutdownHook] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Stopped RedisMessageListenerContainer
2025-07-30 11:22:04.222 [SpringApplicationShutdownHook] DEBUG io.lettuce.core.resource.DefaultClientResources - Initiate shutdown (0, 2, SECONDS)
2025-07-30 11:22:04.230 [SpringApplicationShutdownHook] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Initiate shutdown (0, 2, SECONDS)
2025-07-30 11:22:04.243 [Thread-2] INFO  c.n.common.cache.service.RedisConnectionManager - RedissonClient shutdown completed
2025-07-30 11:22:04.244 [SpringApplicationShutdownHook] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection manager destroyed successfully
2025-07-30 11:23:11.499 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - Starting DemoCommonCacheApplication using Java 21.0.7 with PID 43012 (C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache\target\classes started by Nofi in C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache)
2025-07-30 11:23:11.501 [main] DEBUG com.nanshan.demo.cache.DemoCommonCacheApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-07-30 11:23:11.503 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - The following 1 profile is active: "dev"
2025-07-30 11:23:13.063 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 11:23:13.068 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 11:23:13.123 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-07-30 11:23:13.867 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-30 11:23:13.877 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 11:23:13.881 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 11:23:13.882 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-07-30 11:23:13.994 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 11:23:13.996 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2427 ms
2025-07-30 11:23:14.694 [main] INFO  org.redisson.Version - Redisson 3.24.3
2025-07-30 11:23:15.092 [redisson-netty-2-3] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:23:15.137 [redisson-netty-2-4] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:23:15.161 [redisson-netty-2-7] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:23:15.246 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisDataAccessor
2025-07-30 11:23:15.252 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring CacheManager
2025-07-30 11:23:15.261 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring SessionManager
2025-07-30 11:23:15.324 [main] INFO  c.n.common.cache.service.impl.SessionManagerImpl - SessionManager Redisson optimizations initialized successfully
2025-07-30 11:23:15.328 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisCleaner
2025-07-30 11:23:15.628 [main] INFO  com.nanshan.demo.cache.service.AuthService - 初始化了 3 個示範用戶
2025-07-30 11:23:15.637 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring LockManager
2025-07-30 11:23:15.668 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisConnectionManager
2025-07-30 11:23:15.676 [redisson-netty-2-12] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:23:15.689 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection test successful
2025-07-30 11:23:15.691 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection manager initialized successfully
2025-07-30 11:23:16.016 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-30 11:23:16.742 [main] DEBUG i.l.core.resource.AddressResolverGroupProvider - Starting with netty's non-blocking DNS resolver library
2025-07-30 11:23:16.746 [main] DEBUG io.lettuce.core.resource.KqueueProvider - Starting without optional kqueue library
2025-07-30 11:23:16.748 [main] DEBUG io.lettuce.core.resource.IOUringProvider - Starting without optional io_uring library
2025-07-30 11:23:16.750 [main] DEBUG io.lettuce.core.resource.EpollProvider - Starting without optional epoll library
2025-07-30 11:23:16.752 [main] DEBUG io.lettuce.core.resource.DefaultClientResources - -Dio.netty.eventLoopThreads: 16
2025-07-30 11:23:16.765 [main] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Creating executor io.netty.util.concurrent.DefaultEventExecutorGroup
2025-07-30 11:23:16.781 [main] DEBUG io.lettuce.core.event.jfr.EventRecorderHolder - Starting with JFR support
2025-07-30 11:23:16.977 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Starting RedisMessageListenerContainer...
2025-07-30 11:23:16.978 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Postpone listening for Redis messages until actual listeners are added
2025-07-30 11:23:17.048 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 11:23:17.083 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-30 11:23:17.103 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - Started DemoCommonCacheApplication in 6.443 seconds (process running for 6.98)
2025-07-30 11:23:17.133 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 11:23:17.149 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 11:23:17.149 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 11:23:17.151 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 11:23:17.153 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 11:23:17.157 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 11:23:17.159 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=24ms
2025-07-30 11:23:17.225 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - === Demo Common Cache Application Started Successfully ===
2025-07-30 11:23:17.226 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - Application is running on: http://localhost:8080
2025-07-30 11:23:17.227 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - Available demo endpoints:
2025-07-30 11:23:17.227 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Session Demo: http://localhost:8080/demo/session
2025-07-30 11:23:17.227 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Cache Demo: http://localhost:8080/demo/cache
2025-07-30 11:23:17.228 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Lock Demo: http://localhost:8080/demo/lock
2025-07-30 11:23:17.228 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Cleaner Demo: http://localhost:8080/demo/cleaner
2025-07-30 11:23:17.229 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Comprehensive Demo: http://localhost:8080/demo/all
2025-07-30 11:23:17.229 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Config Check: http://localhost:8080/config/enabled-components
2025-07-30 11:23:17.229 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Health Check: http://localhost:8080/actuator/health
2025-07-30 11:23:20.303 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 1 elements evicted. Object name: sessions
2025-07-30 11:23:20.305 [redisson-netty-2-4] ERROR org.redisson.client.handler.CommandPubSubDecoder - Unable to decode data. channel: [id: 0xb5a23d63, L:/127.0.0.1:3091 - R:localhost/127.0.0.1:6379], reply: ReplayingDecoderByteBuf(ridx=248, widx=248)
com.fasterxml.jackson.core.JsonParseException: Illegal character ((CTRL-CHAR, code 3)): only regular white space (\r, \n, \t) is allowed between tokens
 at [Source: (io.netty.buffer.ByteBufInputStream); line: 1, column: 2]
	at com.fasterxml.jackson.core.JsonParser._constructError(JsonParser.java:2477)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportError(ParserMinimalBase.java:750)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._throwInvalidSpace(ParserMinimalBase.java:728)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._skipWSOrEnd(UTF8StreamJsonParser.java:3121)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser.nextToken(UTF8StreamJsonParser.java:757)
	at com.fasterxml.jackson.databind.ObjectMapper._initForReading(ObjectMapper.java:4912)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4818)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3809)
	at org.redisson.codec.JsonJacksonCodec$2.decode(JsonJacksonCodec.java:99)
	at org.redisson.codec.BaseEventCodec.decode(BaseEventCodec.java:53)
	at org.redisson.codec.MapCacheEventCodec$1.decode(MapCacheEventCodec.java:39)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:394)
	at org.redisson.client.handler.CommandDecoder.decodeList(CommandDecoder.java:442)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:403)
	at org.redisson.client.handler.CommandPubSubDecoder.decodeCommand(CommandPubSubDecoder.java:88)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:144)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:94)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:529)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 11:23:20.327 [redisson-netty-2-4] ERROR org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xb5a23d63, L:/127.0.0.1:3091 - R:localhost/127.0.0.1:6379]
io.netty.handler.codec.DecoderException: com.fasterxml.jackson.core.JsonParseException: Illegal character ((CTRL-CHAR, code 3)): only regular white space (\r, \n, \t) is allowed between tokens
 at [Source: (io.netty.buffer.ByteBufInputStream); line: 1, column: 2]
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:421)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.fasterxml.jackson.core.JsonParseException: Illegal character ((CTRL-CHAR, code 3)): only regular white space (\r, \n, \t) is allowed between tokens
 at [Source: (io.netty.buffer.ByteBufInputStream); line: 1, column: 2]
	at com.fasterxml.jackson.core.JsonParser._constructError(JsonParser.java:2477)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportError(ParserMinimalBase.java:750)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._throwInvalidSpace(ParserMinimalBase.java:728)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._skipWSOrEnd(UTF8StreamJsonParser.java:3121)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser.nextToken(UTF8StreamJsonParser.java:757)
	at com.fasterxml.jackson.databind.ObjectMapper._initForReading(ObjectMapper.java:4912)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4818)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3809)
	at org.redisson.codec.JsonJacksonCodec$2.decode(JsonJacksonCodec.java:99)
	at org.redisson.codec.BaseEventCodec.decode(BaseEventCodec.java:53)
	at org.redisson.codec.MapCacheEventCodec$1.decode(MapCacheEventCodec.java:39)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:394)
	at org.redisson.client.handler.CommandDecoder.decodeList(CommandDecoder.java:442)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:403)
	at org.redisson.client.handler.CommandPubSubDecoder.decodeCommand(CommandPubSubDecoder.java:88)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:144)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:94)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:529)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	... 17 common frames omitted
2025-07-30 11:23:25.316 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:23:30.328 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:23:35.340 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:23:42.355 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:23:52.368 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:24:07.392 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:24:29.403 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:25:02.421 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:25:51.439 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:27:04.445 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:28:17.171 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 11:28:17.174 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 11:28:17.174 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 11:28:17.175 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 11:28:17.175 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 11:28:17.176 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 11:28:17.176 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-30 11:28:53.447 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:31:36.462 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:33:17.183 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 11:33:17.185 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 11:33:17.186 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 11:33:17.186 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 11:33:17.186 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 11:33:17.187 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 11:33:17.187 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-30 11:35:40.475 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:38:17.190 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 11:38:17.192 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 11:38:17.193 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 11:38:17.194 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 11:38:17.194 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 11:38:17.194 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 11:38:17.195 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-30 11:40:17.189 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 11:40:17.189 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 11:40:17.190 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-30 11:40:17.207 [http-nio-8080-exec-1] INFO  c.n.d.cache.controller.ComprehensiveDemoController - 開始執行綜合示範
2025-07-30 11:40:17.207 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 開始執行綜合示範
2025-07-30 11:40:17.244 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Session saved successfully: jwtId=1e8b1da6-79f2-41bf-acc5-5ee32f6d24c4, userId=user-1753846817207, ttl=1800s
2025-07-30 11:40:17.244 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 1: 建立使用者 Session 完成, JWT ID: 1e8b1da6-79f2-41bf-acc5-5ee32f6d24c4
2025-07-30 11:40:17.253 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:product:create, leaseTime=20s
2025-07-30 11:40:17.253 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 2: 在鎖保護下建立產品
2025-07-30 11:40:17.258 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:product:create
2025-07-30 11:40:17.260 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:product:create, lockType=FAIR
2025-07-30 11:40:17.263 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:product:create, lockType=READ
2025-07-30 11:40:17.265 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:product:create, lockType=WRITE
2025-07-30 11:40:17.265 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:product:create
2025-07-30 11:40:17.265 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 2: 使用分散式鎖保護產品建立完成, 產品 ID: PROD-1753846817244
2025-07-30 11:40:17.273 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=PROD-1753846817244, ttl=3600s
2025-07-30 11:40:17.274 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 3: 將產品資訊儲存到快取完成
2025-07-30 11:40:17.294 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=PROD-1753846817244
2025-07-30 11:40:17.295 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 4: 模擬使用者瀏覽產品 (讀取快取) 完成, 快取命中: true
2025-07-30 11:40:17.297 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Write lock acquired: resource=demo:product:stock:PROD-1753846817244, waitTime=5s, leaseTime=15s
2025-07-30 11:40:17.297 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 5: 獲取寫鎖更新產品庫存
2025-07-30 11:40:17.299 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=PROD-1753846817244
2025-07-30 11:40:17.300 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=PROD-1753846817244, ttl=3600s
2025-07-30 11:40:17.302 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:product:stock:PROD-1753846817244, lockType=REENTRANT
2025-07-30 11:40:17.302 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:product:stock:PROD-1753846817244, lockType=FAIR
2025-07-30 11:40:17.302 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:product:stock:PROD-1753846817244, lockType=READ
2025-07-30 11:40:17.304 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Write lock released: resource=demo:product:stock:PROD-1753846817244
2025-07-30 11:40:17.305 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:product:stock:PROD-1753846817244
2025-07-30 11:40:17.305 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 5: 釋放寫鎖
2025-07-30 11:40:17.313 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Session loaded successfully: jwtId=1e8b1da6-79f2-41bf-acc5-5ee32f6d24c4, userId=user-1753846817207
2025-07-30 11:40:17.319 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Session saved successfully: jwtId=1e8b1da6-79f2-41bf-acc5-5ee32f6d24c4, userId=user-1753846817207, ttl=1799s
2025-07-30 11:40:17.319 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 6: 更新 Session 最後活動時間完成, 結果: true
2025-07-30 11:40:17.320 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 11:40:17.321 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 11:40:17.324 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 11:40:17.324 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 11:40:17.325 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 11:40:17.325 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 7: 執行清理操作完成, 清理結果: true
2025-07-30 11:40:17.326 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 8: 獲取統計資訊完成
2025-07-30 11:40:17.333 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up indexes for expired session: jwtId=1e8b1da6-79f2-41bf-acc5-5ee32f6d24c4
2025-07-30 11:40:17.333 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Session deleted successfully: jwtId=1e8b1da6-79f2-41bf-acc5-5ee32f6d24c4
2025-07-30 11:40:17.334 [http-nio-8080-exec-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache removed: type=product, id=PROD-1753846817244
2025-07-30 11:40:17.334 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 步驟 9: 清理示範資源完成
2025-07-30 11:40:17.336 [http-nio-8080-exec-1] INFO  c.n.demo.cache.service.ComprehensiveDemoService - 綜合示範執行完成
2025-07-30 11:40:17.337 [http-nio-8080-exec-1] INFO  c.n.d.cache.controller.ComprehensiveDemoController - 綜合示範執行成功
2025-07-30 11:40:32.505 [http-nio-8080-exec-2] INFO  c.nanshan.demo.cache.controller.LockDemoController - 開始執行分散式鎖完整示範
2025-07-30 11:40:32.506 [http-nio-8080-exec-2] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 1: 可重入鎖示範
2025-07-30 11:40:32.506 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始可重入鎖示範
2025-07-30 11:40:32.507 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired: resource=demo:reentrant:order-process, waitTime=5s, leaseTime=30s
2025-07-30 11:40:32.507 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功獲取可重入鎖: demo:reentrant:order-process
2025-07-30 11:40:32.507 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 處理訂單中...
2025-07-30 11:40:33.510 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 訂單處理完成
2025-07-30 11:40:33.511 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired: resource=demo:reentrant:order-process, waitTime=1s, leaseTime=10s
2025-07-30 11:40:33.511 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功重入鎖: demo:reentrant:order-process
2025-07-30 11:40:33.512 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 更新訂單狀態中...
2025-07-30 11:40:34.025 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 訂單狀態更新完成
2025-07-30 11:40:34.027 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:reentrant:order-process
2025-07-30 11:40:34.028 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=FAIR
2025-07-30 11:40:34.028 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=READ
2025-07-30 11:40:34.028 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=WRITE
2025-07-30 11:40:34.028 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:reentrant:order-process
2025-07-30 11:40:34.029 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放重入鎖: demo:reentrant:order-process
2025-07-30 11:40:34.029 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:reentrant:order-process
2025-07-30 11:40:34.030 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=FAIR
2025-07-30 11:40:34.030 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=READ
2025-07-30 11:40:34.031 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=WRITE
2025-07-30 11:40:34.031 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:reentrant:order-process
2025-07-30 11:40:34.031 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放主鎖: demo:reentrant:order-process
2025-07-30 11:40:34.032 [http-nio-8080-exec-2] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 2: 公平鎖示範
2025-07-30 11:40:34.032 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始公平鎖示範
2025-07-30 11:40:34.033 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Fair lock acquired: resource=demo:fair:queue-process, waitTime=5s, leaseTime=20s
2025-07-30 11:40:34.033 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功獲取公平鎖: demo:fair:queue-process
2025-07-30 11:40:34.033 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 按順序處理隊列中...
2025-07-30 11:40:35.539 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 隊列處理完成
2025-07-30 11:40:35.540 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:fair:queue-process, lockType=REENTRANT
2025-07-30 11:40:35.542 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Fair lock released: resource=demo:fair:queue-process
2025-07-30 11:40:35.542 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:fair:queue-process, lockType=READ
2025-07-30 11:40:35.542 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:fair:queue-process, lockType=WRITE
2025-07-30 11:40:35.542 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:fair:queue-process
2025-07-30 11:40:35.543 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放公平鎖: demo:fair:queue-process
2025-07-30 11:40:35.543 [http-nio-8080-exec-2] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 3: 讀寫鎖示範
2025-07-30 11:40:35.543 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始讀寫鎖示範
2025-07-30 11:40:35.543 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Read lock acquired: resource=demo:readwrite:data-access, waitTime=5s, leaseTime=15s
2025-07-30 11:40:35.543 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功獲取讀鎖: demo:readwrite:data-access
2025-07-30 11:40:35.544 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 讀取資料中...
2025-07-30 11:40:36.352 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 資料讀取完成: 示範資料 - 1753846836352
2025-07-30 11:40:36.353 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=REENTRANT
2025-07-30 11:40:36.354 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=FAIR
2025-07-30 11:40:36.356 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Read lock released: resource=demo:readwrite:data-access
2025-07-30 11:40:36.357 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=WRITE
2025-07-30 11:40:36.357 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:readwrite:data-access
2025-07-30 11:40:36.357 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放讀鎖: demo:readwrite:data-access
2025-07-30 11:40:36.357 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Write lock acquired: resource=demo:readwrite:data-access, waitTime=5s, leaseTime=15s
2025-07-30 11:40:36.358 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功獲取寫鎖: demo:readwrite:data-access
2025-07-30 11:40:36.358 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 寫入資料中...
2025-07-30 11:40:37.571 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 資料寫入完成: 新資料 - 1753846837571
2025-07-30 11:40:37.572 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=REENTRANT
2025-07-30 11:40:37.573 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=FAIR
2025-07-30 11:40:37.574 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=READ
2025-07-30 11:40:37.575 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Write lock released: resource=demo:readwrite:data-access
2025-07-30 11:40:37.576 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:readwrite:data-access
2025-07-30 11:40:37.576 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放寫鎖: demo:readwrite:data-access
2025-07-30 11:40:37.576 [http-nio-8080-exec-2] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 4: 鎖保護執行示範
2025-07-30 11:40:37.576 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始鎖保護執行示範
2025-07-30 11:40:37.577 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:execute:critical-section, leaseTime=20s
2025-07-30 11:40:37.577 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 在鎖保護下執行關鍵業務邏輯
2025-07-30 11:40:37.577 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 執行關鍵操作中...
2025-07-30 11:40:38.586 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 關鍵操作完成: 關鍵操作結果 - 1753846838586
2025-07-30 11:40:38.589 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:execute:critical-section
2025-07-30 11:40:38.590 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:execute:critical-section, lockType=FAIR
2025-07-30 11:40:38.590 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:execute:critical-section, lockType=READ
2025-07-30 11:40:38.590 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:execute:critical-section, lockType=WRITE
2025-07-30 11:40:38.591 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:execute:critical-section
2025-07-30 11:40:38.591 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 鎖保護執行示範完成，結果: 關鍵操作結果 - 1753846838586
2025-07-30 11:40:38.591 [http-nio-8080-exec-2] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 5: 併發鎖測試示範
2025-07-30 11:40:38.591 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始併發鎖測試示範
2025-07-30 11:40:38.594 [redisson-netty-2-12] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:40:38.594 [ForkJoinPool.commonPool-worker-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:concurrent:counter, leaseTime=30s
2025-07-30 11:40:38.594 [ForkJoinPool.commonPool-worker-1] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 1 獲取鎖，開始執行
2025-07-30 11:40:38.594 [redisson-netty-2-13] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:40:38.594 [redisson-netty-2-14] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 11:40:39.100 [ForkJoinPool.commonPool-worker-1] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 1 完成，計數器值: 1
2025-07-30 11:40:39.103 [ForkJoinPool.commonPool-worker-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:concurrent:counter
2025-07-30 11:40:39.103 [ForkJoinPool.commonPool-worker-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=FAIR
2025-07-30 11:40:39.104 [ForkJoinPool.commonPool-worker-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=READ
2025-07-30 11:40:39.104 [ForkJoinPool.commonPool-worker-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:concurrent:counter, leaseTime=30s
2025-07-30 11:40:39.105 [ForkJoinPool.commonPool-worker-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=WRITE
2025-07-30 11:40:39.105 [ForkJoinPool.commonPool-worker-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 2 獲取鎖，開始執行
2025-07-30 11:40:39.105 [ForkJoinPool.commonPool-worker-1] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:concurrent:counter
2025-07-30 11:40:39.616 [ForkJoinPool.commonPool-worker-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 2 完成，計數器值: 2
2025-07-30 11:40:39.619 [ForkJoinPool.commonPool-worker-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:concurrent:counter
2025-07-30 11:40:39.620 [ForkJoinPool.commonPool-worker-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=FAIR
2025-07-30 11:40:39.620 [ForkJoinPool.commonPool-worker-4] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:concurrent:counter, leaseTime=30s
2025-07-30 11:40:39.620 [ForkJoinPool.commonPool-worker-4] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 5 獲取鎖，開始執行
2025-07-30 11:40:39.621 [ForkJoinPool.commonPool-worker-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=READ
2025-07-30 11:40:39.622 [ForkJoinPool.commonPool-worker-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=WRITE
2025-07-30 11:40:39.623 [ForkJoinPool.commonPool-worker-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:concurrent:counter
2025-07-30 11:40:40.129 [ForkJoinPool.commonPool-worker-4] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 5 完成，計數器值: 3
2025-07-30 11:40:40.132 [ForkJoinPool.commonPool-worker-4] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:concurrent:counter
2025-07-30 11:40:40.133 [ForkJoinPool.commonPool-worker-4] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=FAIR
2025-07-30 11:40:40.133 [ForkJoinPool.commonPool-worker-3] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:concurrent:counter, leaseTime=30s
2025-07-30 11:40:40.133 [ForkJoinPool.commonPool-worker-3] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 3 獲取鎖，開始執行
2025-07-30 11:40:40.134 [ForkJoinPool.commonPool-worker-4] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=READ
2025-07-30 11:40:40.134 [ForkJoinPool.commonPool-worker-4] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=WRITE
2025-07-30 11:40:40.135 [ForkJoinPool.commonPool-worker-4] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:concurrent:counter
2025-07-30 11:40:40.634 [ForkJoinPool.commonPool-worker-3] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 3 完成，計數器值: 4
2025-07-30 11:40:40.636 [ForkJoinPool.commonPool-worker-3] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:concurrent:counter
2025-07-30 11:40:40.637 [ForkJoinPool.commonPool-worker-3] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=FAIR
2025-07-30 11:40:40.638 [ForkJoinPool.commonPool-worker-3] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=READ
2025-07-30 11:40:40.638 [ForkJoinPool.commonPool-worker-3] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=WRITE
2025-07-30 11:40:40.638 [ForkJoinPool.commonPool-worker-3] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:concurrent:counter
2025-07-30 11:40:40.639 [ForkJoinPool.commonPool-worker-5] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:concurrent:counter, leaseTime=30s
2025-07-30 11:40:40.639 [ForkJoinPool.commonPool-worker-5] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 4 獲取鎖，開始執行
2025-07-30 11:40:41.142 [ForkJoinPool.commonPool-worker-5] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 4 完成，計數器值: 5
2025-07-30 11:40:41.144 [ForkJoinPool.commonPool-worker-5] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:concurrent:counter
2025-07-30 11:40:41.145 [ForkJoinPool.commonPool-worker-5] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=FAIR
2025-07-30 11:40:41.145 [ForkJoinPool.commonPool-worker-5] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=READ
2025-07-30 11:40:41.146 [ForkJoinPool.commonPool-worker-5] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=WRITE
2025-07-30 11:40:41.146 [ForkJoinPool.commonPool-worker-5] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:concurrent:counter
2025-07-30 11:40:41.146 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 併發鎖測試示範完成，最終計數器值: 5
2025-07-30 11:40:41.146 [http-nio-8080-exec-2] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 6: 鎖狀態檢查示範
2025-07-30 11:40:41.146 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始鎖狀態檢查示範
2025-07-30 11:40:41.148 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 鎖 demo:status:check 是否被持有: false
2025-07-30 11:40:41.148 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired: resource=demo:status:check, waitTime=1s, leaseTime=10s
2025-07-30 11:40:41.150 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 獲取鎖後狀態 - 是否被持有: true, 是否被當前線程持有: true, 剩餘租約時間: 9秒
2025-07-30 11:40:41.151 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:status:check
2025-07-30 11:40:41.151 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:status:check, lockType=FAIR
2025-07-30 11:40:41.152 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:status:check, lockType=READ
2025-07-30 11:40:41.153 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:status:check, lockType=WRITE
2025-07-30 11:40:41.153 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:status:check
2025-07-30 11:40:41.153 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 鎖釋放結果: true
2025-07-30 11:40:41.154 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放鎖後狀態 - 是否被持有: false
2025-07-30 11:40:41.155 [http-nio-8080-exec-2] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 7: 強制解鎖示範
2025-07-30 11:40:41.155 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始強制解鎖示範
2025-07-30 11:40:41.157 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired: resource=demo:force:unlock, waitTime=1s, leaseTime=30s
2025-07-30 11:40:41.157 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功獲取鎖: demo:force:unlock
2025-07-30 11:40:41.158 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 鎖狀態 - 是否被持有: true, 是否被當前線程持有: true
2025-07-30 11:40:41.161 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Force unlocked resource: demo:force:unlock
2025-07-30 11:40:41.161 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 強制解鎖結果: true
2025-07-30 11:40:41.164 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.LockDemoService - 強制解鎖後狀態 - 是否被持有: false
2025-07-30 11:40:41.164 [http-nio-8080-exec-2] INFO  c.nanshan.demo.cache.controller.LockDemoController - 分散式鎖完整示範執行完成，成功: true
2025-07-30 11:41:46.479 [redisson-netty-2-14] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 11:43:17.205 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 11:43:17.208 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 11:43:17.209 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 11:43:17.211 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 11:43:17.211 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 11:43:17.212 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 11:43:17.212 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-30 11:48:17.216 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 11:48:17.219 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 11:48:17.219 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 11:48:17.220 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 11:48:17.220 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 11:48:17.221 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 11:48:17.221 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-30 11:57:57.273 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 12:00:19.011 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:00:19.013 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:00:19.013 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:00:19.014 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:00:19.014 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:00:19.015 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:00:19.015 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-30 12:03:58.688 [http-nio-8080-exec-7] INFO  c.nanshan.demo.cache.controller.LockDemoController - 開始執行分散式鎖完整示範
2025-07-30 12:03:58.688 [http-nio-8080-exec-7] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 1: 可重入鎖示範
2025-07-30 12:03:58.689 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始可重入鎖示範
2025-07-30 12:03:58.690 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired: resource=demo:reentrant:order-process, waitTime=5s, leaseTime=30s
2025-07-30 12:03:58.690 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功獲取可重入鎖: demo:reentrant:order-process
2025-07-30 12:03:58.690 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 處理訂單中...
2025-07-30 12:03:59.703 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 訂單處理完成
2025-07-30 12:03:59.705 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired: resource=demo:reentrant:order-process, waitTime=1s, leaseTime=10s
2025-07-30 12:03:59.705 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功重入鎖: demo:reentrant:order-process
2025-07-30 12:03:59.705 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 更新訂單狀態中...
2025-07-30 12:04:00.220 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 訂單狀態更新完成
2025-07-30 12:04:00.221 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:reentrant:order-process
2025-07-30 12:04:00.222 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=FAIR
2025-07-30 12:04:00.222 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=READ
2025-07-30 12:04:00.222 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=WRITE
2025-07-30 12:04:00.222 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:reentrant:order-process
2025-07-30 12:04:00.222 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放重入鎖: demo:reentrant:order-process
2025-07-30 12:04:00.224 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:reentrant:order-process
2025-07-30 12:04:00.224 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=FAIR
2025-07-30 12:04:00.225 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=READ
2025-07-30 12:04:00.225 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:reentrant:order-process, lockType=WRITE
2025-07-30 12:04:00.225 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:reentrant:order-process
2025-07-30 12:04:00.225 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放主鎖: demo:reentrant:order-process
2025-07-30 12:04:00.225 [http-nio-8080-exec-7] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 2: 公平鎖示範
2025-07-30 12:04:00.225 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始公平鎖示範
2025-07-30 12:04:00.226 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Fair lock acquired: resource=demo:fair:queue-process, waitTime=5s, leaseTime=20s
2025-07-30 12:04:00.226 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功獲取公平鎖: demo:fair:queue-process
2025-07-30 12:04:00.226 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 按順序處理隊列中...
2025-07-30 12:04:01.729 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 隊列處理完成
2025-07-30 12:04:01.730 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:fair:queue-process, lockType=REENTRANT
2025-07-30 12:04:01.732 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Fair lock released: resource=demo:fair:queue-process
2025-07-30 12:04:01.732 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:fair:queue-process, lockType=READ
2025-07-30 12:04:01.733 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:fair:queue-process, lockType=WRITE
2025-07-30 12:04:01.733 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:fair:queue-process
2025-07-30 12:04:01.733 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放公平鎖: demo:fair:queue-process
2025-07-30 12:04:01.733 [http-nio-8080-exec-7] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 3: 讀寫鎖示範
2025-07-30 12:04:01.733 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始讀寫鎖示範
2025-07-30 12:04:01.734 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Read lock acquired: resource=demo:readwrite:data-access, waitTime=5s, leaseTime=15s
2025-07-30 12:04:01.734 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功獲取讀鎖: demo:readwrite:data-access
2025-07-30 12:04:01.734 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 讀取資料中...
2025-07-30 12:04:02.542 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 資料讀取完成: 示範資料 - 1753848242542
2025-07-30 12:04:02.543 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=REENTRANT
2025-07-30 12:04:02.544 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=FAIR
2025-07-30 12:04:02.545 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Read lock released: resource=demo:readwrite:data-access
2025-07-30 12:04:02.545 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=WRITE
2025-07-30 12:04:02.545 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:readwrite:data-access
2025-07-30 12:04:02.546 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放讀鎖: demo:readwrite:data-access
2025-07-30 12:04:02.546 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Write lock acquired: resource=demo:readwrite:data-access, waitTime=5s, leaseTime=15s
2025-07-30 12:04:02.546 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功獲取寫鎖: demo:readwrite:data-access
2025-07-30 12:04:02.546 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 寫入資料中...
2025-07-30 12:04:03.758 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 資料寫入完成: 新資料 - 1753848243758
2025-07-30 12:04:03.759 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=REENTRANT
2025-07-30 12:04:03.759 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=FAIR
2025-07-30 12:04:03.759 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:readwrite:data-access, lockType=READ
2025-07-30 12:04:03.760 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Write lock released: resource=demo:readwrite:data-access
2025-07-30 12:04:03.760 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:readwrite:data-access
2025-07-30 12:04:03.760 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放寫鎖: demo:readwrite:data-access
2025-07-30 12:04:03.761 [http-nio-8080-exec-7] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 4: 鎖保護執行示範
2025-07-30 12:04:03.761 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始鎖保護執行示範
2025-07-30 12:04:03.761 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:execute:critical-section, leaseTime=20s
2025-07-30 12:04:03.761 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 在鎖保護下執行關鍵業務邏輯
2025-07-30 12:04:03.762 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 執行關鍵操作中...
2025-07-30 12:04:04.771 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 關鍵操作完成: 關鍵操作結果 - 1753848244771
2025-07-30 12:04:04.773 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:execute:critical-section
2025-07-30 12:04:04.773 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:execute:critical-section, lockType=FAIR
2025-07-30 12:04:04.774 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:execute:critical-section, lockType=READ
2025-07-30 12:04:04.774 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:execute:critical-section, lockType=WRITE
2025-07-30 12:04:04.774 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:execute:critical-section
2025-07-30 12:04:04.774 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 鎖保護執行示範完成，結果: 關鍵操作結果 - 1753848244771
2025-07-30 12:04:04.774 [http-nio-8080-exec-7] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 5: 併發鎖測試示範
2025-07-30 12:04:04.775 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始併發鎖測試示範
2025-07-30 12:04:04.776 [ForkJoinPool.commonPool-worker-9] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:concurrent:counter, leaseTime=30s
2025-07-30 12:04:04.776 [ForkJoinPool.commonPool-worker-9] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 1 獲取鎖，開始執行
2025-07-30 12:04:04.777 [redisson-netty-2-24] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 12:04:04.777 [redisson-netty-2-25] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 12:04:04.777 [redisson-netty-2-26] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-30 12:04:05.277 [ForkJoinPool.commonPool-worker-9] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 1 完成，計數器值: 1
2025-07-30 12:04:05.279 [ForkJoinPool.commonPool-worker-9] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:concurrent:counter
2025-07-30 12:04:05.280 [ForkJoinPool.commonPool-worker-9] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=FAIR
2025-07-30 12:04:05.281 [ForkJoinPool.commonPool-worker-10] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:concurrent:counter, leaseTime=30s
2025-07-30 12:04:05.281 [ForkJoinPool.commonPool-worker-9] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=READ
2025-07-30 12:04:05.281 [ForkJoinPool.commonPool-worker-10] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 2 獲取鎖，開始執行
2025-07-30 12:04:05.282 [ForkJoinPool.commonPool-worker-9] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=WRITE
2025-07-30 12:04:05.282 [ForkJoinPool.commonPool-worker-9] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:concurrent:counter
2025-07-30 12:04:05.794 [ForkJoinPool.commonPool-worker-10] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 2 完成，計數器值: 2
2025-07-30 12:04:05.796 [ForkJoinPool.commonPool-worker-10] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:concurrent:counter
2025-07-30 12:04:05.797 [ForkJoinPool.commonPool-worker-10] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=FAIR
2025-07-30 12:04:05.798 [ForkJoinPool.commonPool-worker-12] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:concurrent:counter, leaseTime=30s
2025-07-30 12:04:05.798 [ForkJoinPool.commonPool-worker-12] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 4 獲取鎖，開始執行
2025-07-30 12:04:05.798 [ForkJoinPool.commonPool-worker-10] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=READ
2025-07-30 12:04:05.798 [ForkJoinPool.commonPool-worker-10] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=WRITE
2025-07-30 12:04:05.798 [ForkJoinPool.commonPool-worker-10] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:concurrent:counter
2025-07-30 12:04:06.308 [ForkJoinPool.commonPool-worker-12] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 4 完成，計數器值: 3
2025-07-30 12:04:06.311 [ForkJoinPool.commonPool-worker-12] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:concurrent:counter
2025-07-30 12:04:06.311 [ForkJoinPool.commonPool-worker-12] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=FAIR
2025-07-30 12:04:06.312 [ForkJoinPool.commonPool-worker-11] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:concurrent:counter, leaseTime=30s
2025-07-30 12:04:06.312 [ForkJoinPool.commonPool-worker-12] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=READ
2025-07-30 12:04:06.312 [ForkJoinPool.commonPool-worker-11] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 3 獲取鎖，開始執行
2025-07-30 12:04:06.312 [ForkJoinPool.commonPool-worker-12] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=WRITE
2025-07-30 12:04:06.312 [ForkJoinPool.commonPool-worker-12] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:concurrent:counter
2025-07-30 12:04:06.824 [ForkJoinPool.commonPool-worker-11] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 3 完成，計數器值: 4
2025-07-30 12:04:06.826 [ForkJoinPool.commonPool-worker-11] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:concurrent:counter
2025-07-30 12:04:06.826 [ForkJoinPool.commonPool-worker-11] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=FAIR
2025-07-30 12:04:06.826 [ForkJoinPool.commonPool-worker-14] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired (blocking): resource=demo:concurrent:counter, leaseTime=30s
2025-07-30 12:04:06.827 [ForkJoinPool.commonPool-worker-14] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 5 獲取鎖，開始執行
2025-07-30 12:04:06.827 [ForkJoinPool.commonPool-worker-11] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=READ
2025-07-30 12:04:06.827 [ForkJoinPool.commonPool-worker-11] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=WRITE
2025-07-30 12:04:06.827 [ForkJoinPool.commonPool-worker-11] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:concurrent:counter
2025-07-30 12:04:07.338 [ForkJoinPool.commonPool-worker-14] INFO  com.nanshan.demo.cache.service.LockDemoService - 任務 5 完成，計數器值: 5
2025-07-30 12:04:07.340 [ForkJoinPool.commonPool-worker-14] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:concurrent:counter
2025-07-30 12:04:07.341 [ForkJoinPool.commonPool-worker-14] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=FAIR
2025-07-30 12:04:07.341 [ForkJoinPool.commonPool-worker-14] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=READ
2025-07-30 12:04:07.342 [ForkJoinPool.commonPool-worker-14] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:concurrent:counter, lockType=WRITE
2025-07-30 12:04:07.342 [ForkJoinPool.commonPool-worker-14] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:concurrent:counter
2025-07-30 12:04:07.342 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 併發鎖測試示範完成，最終計數器值: 5
2025-07-30 12:04:07.342 [http-nio-8080-exec-7] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 6: 鎖狀態檢查示範
2025-07-30 12:04:07.342 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始鎖狀態檢查示範
2025-07-30 12:04:07.344 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 鎖 demo:status:check 是否被持有: false
2025-07-30 12:04:07.344 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired: resource=demo:status:check, waitTime=1s, leaseTime=10s
2025-07-30 12:04:07.346 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 獲取鎖後狀態 - 是否被持有: true, 是否被當前線程持有: true, 剩餘租約時間: 9秒
2025-07-30 12:04:07.347 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Reentrant lock released: resource=demo:status:check
2025-07-30 12:04:07.347 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:status:check, lockType=FAIR
2025-07-30 12:04:07.348 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:status:check, lockType=READ
2025-07-30 12:04:07.348 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock not held by current thread: resource=demo:status:check, lockType=WRITE
2025-07-30 12:04:07.348 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Successfully unlocked resource: demo:status:check
2025-07-30 12:04:07.348 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 鎖釋放結果: true
2025-07-30 12:04:07.349 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 釋放鎖後狀態 - 是否被持有: false
2025-07-30 12:04:07.349 [http-nio-8080-exec-7] INFO  c.nanshan.demo.cache.controller.LockDemoController - 步驟 7: 強制解鎖示範
2025-07-30 12:04:07.349 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 開始強制解鎖示範
2025-07-30 12:04:07.350 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Lock acquired: resource=demo:force:unlock, waitTime=1s, leaseTime=30s
2025-07-30 12:04:07.350 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 成功獲取鎖: demo:force:unlock
2025-07-30 12:04:07.351 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 鎖狀態 - 是否被持有: true, 是否被當前線程持有: true
2025-07-30 12:04:07.355 [http-nio-8080-exec-7] DEBUG c.n.common.cache.service.impl.LockManagerImpl - Force unlocked resource: demo:force:unlock
2025-07-30 12:04:07.362 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 強制解鎖結果: true
2025-07-30 12:04:07.363 [http-nio-8080-exec-7] INFO  com.nanshan.demo.cache.service.LockDemoService - 強制解鎖後狀態 - 是否被持有: false
2025-07-30 12:04:07.364 [http-nio-8080-exec-7] INFO  c.nanshan.demo.cache.controller.LockDemoController - 分散式鎖完整示範執行完成，成功: true
2025-07-30 12:05:19.015 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:05:19.018 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:05:19.018 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:05:19.019 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:05:19.019 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:05:19.020 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:05:19.020 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-30 12:10:19.032 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:10:19.034 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:10:19.034 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:10:19.034 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:10:19.035 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:10:19.035 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:10:19.035 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-30 12:11:40.275 [redisson-netty-2-25] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 12:15:19.036 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:15:19.039 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:15:19.039 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:15:19.040 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:15:19.040 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:15:19.040 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:15:19.040 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-30 12:20:19.047 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:20:19.049 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:20:19.049 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:20:19.050 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:20:19.050 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:20:19.050 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:20:19.050 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-30 12:25:19.055 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:25:19.057 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:25:19.057 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:25:19.057 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:25:19.057 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:25:19.058 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:25:19.058 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-30 12:30:19.068 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:30:19.070 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:30:19.071 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:30:19.072 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:30:19.072 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:30:19.073 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:30:19.073 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-30 12:32:14.281 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 12:35:19.087 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:35:19.089 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:35:19.089 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:35:19.089 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:35:19.090 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:35:19.090 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:35:19.090 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-30 12:40:19.097 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:40:19.099 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:40:19.099 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:40:19.099 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:40:19.100 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:40:19.100 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:40:19.100 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-30 12:45:19.117 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:45:19.118 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:45:19.118 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:45:19.119 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:45:19.119 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:45:19.120 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:45:19.120 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-30 12:50:19.130 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:50:19.132 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:50:19.132 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:50:19.133 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:50:19.133 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:50:19.133 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:50:19.133 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-30 12:55:19.146 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 12:55:19.148 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 12:55:19.148 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 12:55:19.149 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 12:55:19.149 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 12:55:19.149 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 12:55:19.149 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-30 13:00:19.166 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 13:00:19.167 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 13:00:19.167 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 13:00:19.168 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 13:00:19.168 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 13:00:19.168 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 13:00:19.169 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-30 13:02:14.284 [redisson-netty-2-25] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-30 13:05:19.177 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 13:05:19.178 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 13:05:19.179 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 13:05:19.179 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 13:05:19.179 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 13:05:19.180 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 13:05:19.180 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-30 13:10:19.187 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 13:10:19.189 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 13:10:19.189 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 13:10:19.189 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 13:10:19.189 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 13:10:19.191 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 13:10:19.191 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
