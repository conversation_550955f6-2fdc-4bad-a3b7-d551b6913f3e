package com.nanshan.common.cache.service;

import com.nanshan.common.cache.annotation.EnableRedisSupport;
import com.nanshan.common.cache.config.TestConfig;
import com.nanshan.common.cache.model.SessionObject;
import com.nanshan.common.cache.model.SessionObjectBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SessionManager 測試
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = SessionManagerTest.SessionTestApplication.class)
@Import(TestConfig.class)
@TestPropertySource(properties = {
    "common.cache.session.default-time-to-live=10",
    "common.cache.session.auto-renewal=true",
    "common.cache.session.renewal-threshold=0.5"
})
class SessionManagerTest {

    @SpringBootApplication
    @EnableRedisSupport
    @Import(TestConfig.class)
    public static class SessionTestApplication {

        public static void main(String[] args) {
            SpringApplication.run(SessionTestApplication.class, args);
        }
    }

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private RedisDataAccessor redisDataAccessor;

    private SessionObject testSession;
    private final String testJwtId = "test-jwt-123";
    private final String testUserId = "user123";
    private final String testClientId = "portal";

    @BeforeEach
    void setUp() {
        // 清理測試資料
        sessionManager.deleteSession(testJwtId);

        // 建立測試 Session
        testSession = SessionObjectBuilder.createBasicSession(testUserId, testClientId, testJwtId);
    }

    @Test
    void testSaveAndLoadSession() {
        // 儲存 Session
        sessionManager.saveSession(testJwtId, testSession, 60);

        // 載入 Session
        Optional<SessionObject> loaded = sessionManager.loadSession(testJwtId);

        assertTrue(loaded.isPresent());
        assertEquals(testUserId, loaded.get().getUserId());
        assertEquals(testClientId, loaded.get().getClientId());
        assertEquals(testJwtId, loaded.get().getJwtId());
    }

    @Test
    void testSaveSessionWithDefaultTTL() {
        // 使用預設 TTL 儲存 Session
        sessionManager.saveSession(testJwtId, testSession);

        // 驗證 Session 存在
        assertTrue(sessionManager.existsSession(testJwtId));

        // 檢查 TTL
        long ttl = sessionManager.getSessionRemainingTimeToLive(testJwtId, TimeUnit.SECONDS);
        assertTrue(ttl > 0 && ttl <= 10); // 測試配置中設定為 10 秒
    }

    @Test
    void testSaveSessionWithDuration() {
        // 使用 Duration 儲存 Session
        sessionManager.saveSession(testJwtId, testSession, Duration.ofMinutes(5));

        // 驗證 Session 存在
        assertTrue(sessionManager.existsSession(testJwtId));

        // 檢查 TTL
        long ttl = sessionManager.getSessionRemainingTimeToLive(testJwtId, TimeUnit.SECONDS);
        assertTrue(ttl > 290 && ttl <= 300); // 約 5 分鐘
    }

    @Test
    void testDeleteSession() {
        // 儲存 Session
        sessionManager.saveSession(testJwtId, testSession, 60);
        assertTrue(sessionManager.existsSession(testJwtId));

        // 刪除 Session
        boolean deleted = sessionManager.deleteSession(testJwtId);
        assertTrue(deleted);
        assertFalse(sessionManager.existsSession(testJwtId));

        // 再次刪除應該返回 false
        boolean deletedAgain = sessionManager.deleteSession(testJwtId);
        assertFalse(deletedAgain);
    }

    @Test
    void testUpdateLastActiveTime() {
        // 儲存 Session
        sessionManager.saveSession(testJwtId, testSession, 60);

        // 載入原始 Session
        Optional<SessionObject> original = sessionManager.loadSession(testJwtId);
        assertTrue(original.isPresent());

        // 等待一小段時間
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 更新最後活動時間
        boolean updated = sessionManager.updateLastActiveTime(testJwtId);
        assertTrue(updated);

        // 載入更新後的 Session
        Optional<SessionObject> updatedSession = sessionManager.loadSession(testJwtId);
        assertTrue(updatedSession.isPresent());
        assertTrue(updatedSession.get().getLastActiveTime().isAfter(original.get().getLastActiveTime()));
    }

    @Test
    void testRenewSession() {
        // 儲存 Session
        sessionManager.saveSession(testJwtId, testSession, 30);

        // 獲取原始 TTL
        long originalTtl = sessionManager.getSessionRemainingTimeToLive(testJwtId, TimeUnit.SECONDS);

        // 續期 Session
        boolean renewed = sessionManager.renewSession(testJwtId, 60);
        assertTrue(renewed);

        // 檢查新的 TTL
        long newTtl = sessionManager.getSessionRemainingTimeToLive(testJwtId, TimeUnit.SECONDS);
        assertTrue(newTtl > originalTtl);
    }

    @Test
    void testRenewSessionWithDefaultTTL() {
        // 儲存 Session
        sessionManager.saveSession(testJwtId, testSession, 5);

        // 使用預設 TTL 續期
        boolean renewed = sessionManager.renewSession(testJwtId);
        assertTrue(renewed);

        // 檢查 TTL
        long ttl = sessionManager.getSessionRemainingTimeToLive(testJwtId, TimeUnit.SECONDS);
        assertTrue(ttl > 5); // 應該比原來的 5 秒更長
    }

    @Test
    void testFindSessionsByUserId() {
        // 建立多個 Session
        String jwtId1 = "jwt1";
        String jwtId2 = "jwt2";
        String jwtId3 = "jwt3";

        SessionObject session1 = SessionObjectBuilder.createBasicSession(testUserId, "client1", jwtId1);
        SessionObject session2 = SessionObjectBuilder.createBasicSession(testUserId, "client2", jwtId2);
        SessionObject session3 = SessionObjectBuilder.createBasicSession("otherUser", "client3", jwtId3);

        sessionManager.saveSession(jwtId1, session1, 60);
        sessionManager.saveSession(jwtId2, session2, 60);
        sessionManager.saveSession(jwtId3, session3, 60);

        // 查找指定使用者的 Session
        List<SessionObject> userSessions = sessionManager.findSessionsByUserId(testUserId);

        assertEquals(2, userSessions.size());
        assertTrue(userSessions.stream().allMatch(s -> testUserId.equals(s.getUserId())));

        // 清理
        sessionManager.deleteSession(jwtId1);
        sessionManager.deleteSession(jwtId2);
        sessionManager.deleteSession(jwtId3);
    }

    @Test
    void testFindSessionsByClientId() {
        // 建立多個 Session
        String jwtId1 = "jwt1";
        String jwtId2 = "jwt2";
        String jwtId3 = "jwt3";

        SessionObject session1 = SessionObjectBuilder.createBasicSession("user1", testClientId, jwtId1);
        SessionObject session2 = SessionObjectBuilder.createBasicSession("user2", testClientId, jwtId2);
        SessionObject session3 = SessionObjectBuilder.createBasicSession("user3", "otherClient", jwtId3);

        sessionManager.saveSession(jwtId1, session1, 60);
        sessionManager.saveSession(jwtId2, session2, 60);
        sessionManager.saveSession(jwtId3, session3, 60);

        // 查找指定客戶端的 Session
        List<SessionObject> clientSessions = sessionManager.findSessionsByClientId(testClientId);

        assertEquals(2, clientSessions.size());
        assertTrue(clientSessions.stream().allMatch(s -> testClientId.equals(s.getClientId())));

        // 清理
        sessionManager.deleteSession(jwtId1);
        sessionManager.deleteSession(jwtId2);
        sessionManager.deleteSession(jwtId3);
    }

    @Test
    void testDeleteSessionsByUserId() {
        // 建立多個 Session
        String jwtId1 = "jwt1";
        String jwtId2 = "jwt2";

        SessionObject session1 = SessionObjectBuilder.createBasicSession(testUserId, "client1", jwtId1);
        SessionObject session2 = SessionObjectBuilder.createBasicSession(testUserId, "client2", jwtId2);

        sessionManager.saveSession(jwtId1, session1, 60);
        sessionManager.saveSession(jwtId2, session2, 60);

        // 刪除使用者的所有 Session
        long deletedCount = sessionManager.deleteSessionsByUserId(testUserId);

        assertEquals(2, deletedCount);
        assertFalse(sessionManager.existsSession(jwtId1));
        assertFalse(sessionManager.existsSession(jwtId2));
    }

    @Test
    void testGetSessionStats() {
        // 儲存一些 Session
        sessionManager.saveSession("jwt1", SessionObjectBuilder.createBasicSession("user1", "client1", "jwt1"), 60);
        sessionManager.saveSession("jwt2", SessionObjectBuilder.createBasicSession("user2", "client2", "jwt2"), 60);

        // 獲取統計資訊
        SessionManager.SessionStats stats = sessionManager.getSessionStats();

        assertNotNull(stats);
        assertTrue(stats.getTotalSessions() >= 2);
        assertTrue(stats.getActiveSessions() >= 2);

        // 清理
        sessionManager.deleteSession("jwt1");
        sessionManager.deleteSession("jwt2");
    }
}
