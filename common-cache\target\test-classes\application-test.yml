# 測試環境配置
spring:
  profiles:
    active: test
  redis:
    host: localhost
    port: 6380
    password: test123
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# Common Cache 測試配置
common:
  cache:
    redis:
      host: localhost
      port: 6380
      password: test123
      database: 1
      timeout: 2000
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
    
    cache:
      default-time-to-live: 300
      max-idle-time: 600
      
    session:
      default-time-to-live: 1800
      auto-renewal: true
      renewal-threshold: 0.5
      
    lock:
      default-wait-time: 10
      default-lease-time: 30
      fair-lock-enabled: true
      
    cleaner:
      enabled: true
      batch-size: 100
      
    env-guard:
      production-environment: false
      dangerous-operations-enabled: true

# 日誌配置
logging:
  level:
    com.nanshan.common.cache: DEBUG
    org.redisson: INFO
    org.springframework.data.redis: DEBUG
