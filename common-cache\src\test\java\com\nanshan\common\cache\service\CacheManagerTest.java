package com.nanshan.common.cache.service;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import com.nanshan.common.cache.annotation.EnableRedisSupport;
import com.nanshan.common.cache.config.TestConfig;

/**
 * CacheManager 測試
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = CacheManagerTest.CacheTestApplication.class)
@Import(TestConfig.class)
@TestPropertySource(properties = {
    "common.cache.cache.default-time-to-live=60",
    "common.cache.cache.auto-renewal=true",
    "common.cache.cache.renewal-threshold=0.5"
})
public class CacheManagerTest {

    @SpringBootApplication
    @EnableRedisSupport
    @Import(TestConfig.class)
    public static class CacheTestApplication {

        public static void main(String[] args) {
            SpringApplication.run(CacheTestApplication.class, args);
        }
    }

    @Autowired
    private CacheManager cacheManager;

    private final String testType = "test";
    private final String testId = "123";
    private final String testValue = "test-value";

    @BeforeEach
    void setUp() {
        // 清理測試資料
        cacheManager.remove(testType, testId);
    }

    @AfterEach
    void tearDown() {
        // 清理測試資料
        cacheManager.remove(testType, testId);
    }

    @Test
    void testPutAndGet() {
        // 儲存快取
        cacheManager.put(testType, testId, testValue, 60, TimeUnit.SECONDS);

        // 獲取快取
        Optional<String> cached = cacheManager.get(testType, testId, String.class);
        assertTrue(cached.isPresent());
        assertEquals(testValue, cached.get());
    }

    @Test
    void testPutWithDefaultTTL() {
        // 使用預設 TTL 儲存快取
        cacheManager.put(testType, testId, testValue);

        // 驗證快取存在
        assertTrue(cacheManager.exists(testType, testId));

        // 檢查 TTL
        long ttl = cacheManager.getRemainingTimeToLive(testType, testId, TimeUnit.SECONDS);
        assertTrue(ttl > 0 && ttl <= 60);
    }

    @Test
    void testPutWithDuration() {
        // 使用 Duration 儲存快取
        cacheManager.put(testType, testId, testValue, Duration.ofMinutes(2));

        // 驗證快取存在
        assertTrue(cacheManager.exists(testType, testId));

        // 檢查 TTL
        long ttl = cacheManager.getRemainingTimeToLive(testType, testId, TimeUnit.SECONDS);
        assertTrue(ttl > 110 && ttl <= 120); // 約 2 分鐘
    }

    @Test
    void testRemove() {
        // 儲存快取
        cacheManager.put(testType, testId, testValue, 60, TimeUnit.SECONDS);
        assertTrue(cacheManager.exists(testType, testId));

        // 刪除快取
        boolean removed = cacheManager.remove(testType, testId);
        assertTrue(removed);
        assertFalse(cacheManager.exists(testType, testId));

        // 再次刪除應該返回 false
        boolean removedAgain = cacheManager.remove(testType, testId);
        assertFalse(removedAgain);
    }

    @Test
    void testGetOrCompute() {
        // 第一次調用，應該執行計算函數
        cacheManager.put(testType, testId, "computed-value", 60, TimeUnit.SECONDS);
        Optional<String> result1Opt = cacheManager.get(testType, testId, String.class);
        String result1 = result1Opt.orElse("not-found");

        assertEquals("computed-value", result1);

        // 第二次調用，應該從快取獲取
        Optional<String> result2Opt = cacheManager.get(testType, testId, String.class);
        String result2 = result2Opt.orElse("not-found");

        assertEquals("computed-value", result2);
    }

    @Test
    void testMultiPut() {
        Map<String, String> data = Map.of(
                "key1", "value1",
                "key2", "value2",
                "key3", "value3"
        );

        // 批量儲存
        cacheManager.multiPut(testType, data, 60, TimeUnit.SECONDS);

        // 驗證所有資料都已儲存
        data.forEach((key, expectedValue) -> {
            Optional<String> cached = cacheManager.get(testType, key, String.class);
            assertTrue(cached.isPresent());
            assertEquals(expectedValue, cached.get());
        });

        // 清理
        data.keySet().forEach(key -> cacheManager.remove(testType, key));
    }

    @Test
    void testMultiGet() {
        Map<String, String> data = Map.of(
                "key1", "value1",
                "key2", "value2",
                "key3", "value3"
        );

        // 儲存測試資料
        data.forEach((key, value)
                -> cacheManager.put(testType, key, value, 60, TimeUnit.SECONDS));

        // 批量獲取
        Map<String, String> result = cacheManager.multiGet(testType, data.keySet(), String.class);

        // 驗證結果
        assertEquals(data.size(), result.size());
        data.forEach((key, expectedValue) -> {
            assertTrue(result.containsKey(key));
            assertEquals(expectedValue, result.get(key));
        });

        // 清理
        data.keySet().forEach(key -> cacheManager.remove(testType, key));
    }

    @Test
    void testMultiRemove() {
        Map<String, String> data = Map.of(
                "key1", "value1",
                "key2", "value2",
                "key3", "value3"
        );

        // 儲存測試資料
        data.forEach((key, value)
                -> cacheManager.put(testType, key, value, 60, TimeUnit.SECONDS));

        // 驗證資料存在
        data.keySet().forEach(key
                -> assertTrue(cacheManager.exists(testType, key)));

        // 批量刪除
        long evictedCount = data.keySet().stream().mapToLong(key -> cacheManager.remove(testType, key) ? 1 : 0).sum();
        assertEquals(data.size(), evictedCount);

        // 驗證資料已刪除
        data.keySet().forEach(key
                -> assertFalse(cacheManager.exists(testType, key)));
    }

    @Test
    void testRenewCache() {
        // 儲存快取
        cacheManager.put(testType, testId, testValue, 30, TimeUnit.SECONDS);

        // 檢查初始 TTL
        long initialTTL = cacheManager.getRemainingTimeToLive(testType, testId, TimeUnit.SECONDS);
        assertTrue(initialTTL > 25 && initialTTL <= 30);

        // 續期
        boolean renewed = cacheManager.renew(testType, testId, 60, TimeUnit.SECONDS);
        assertTrue(renewed);

        // 檢查續期後的 TTL
        long renewedTTL = cacheManager.getRemainingTimeToLive(testType, testId, TimeUnit.SECONDS);
        assertTrue(renewedTTL > 55 && renewedTTL <= 60);
    }

    @Test
    void testCleanupExpiredCaches() {
        // 儲存一些短期快取
        cacheManager.put(testType, "short1", "value1", 1, TimeUnit.SECONDS);
        cacheManager.put(testType, "short2", "value2", 1, TimeUnit.SECONDS);

        // 等待過期
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 執行清理
        long cleanedCount = cacheManager.cleanupExpired();
        assertTrue(cleanedCount >= 0); // 可能為 0，因為 Redis 可能已經自動清理
    }

    @Test
    void testGetStats() {
        // 儲存一些測試資料
        cacheManager.put(testType, "stats1", "value1", 60, TimeUnit.SECONDS);
        cacheManager.put(testType, "stats2", "value2", 60, TimeUnit.SECONDS);

        // 獲取統計資訊
        CacheManager.CacheStats stats = cacheManager.getStats();
        assertNotNull(stats);
        assertTrue(stats.getTotalRequests() >= 0);
        assertTrue(stats.getHitCount() >= 0);
        assertTrue(stats.getMissCount() >= 0);

        // 清理
        cacheManager.remove(testType, "stats1");
        cacheManager.remove(testType, "stats2");
    }
}
