# Redis Flush 操作測試配置
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      username:
      password:
      timeout: 3000ms

# Common Cache 配置 - 測試環境
common:
  cache:
    # Redis 環境保護設定 - 測試環境允許危險操作
    env-guard:
      enabled: true
      production-profiles: 
        - prod
        - production
      dangerous-operations-enabled: true  # 測試環境允許危險操作
    
    # Session 管理配置
    session:
      default-ttl: 60  # 測試環境較短的 TTL（1分鐘）
      key-prefix: "test:session"
      cleanup-interval: 30  # 30 秒清理一次
      auto-renewal: true
      renewal-threshold: 0.5  # 剩餘 50% TTL 時自動續期
    
    # 快取管理配置
    cache:
      default-ttl: 120  # 測試環境較短的 TTL（2分鐘）
      key-prefix: "test:cache"
      auto-renewal: true
      renewal-threshold: 0.5  # 剩餘 50% TTL 時自動續期
      max-size: 1000
    
    # 鎖管理配置
    lock:
      default-lease-time: 10  # 測試環境較短的租約時間
      default-wait-time: 5    # 測試環境較短的等待時間
      key-prefix: "test:lock"
      fair-lock-enabled: true
    
    # 清理器配置
    cleaner:
      enabled: true
      schedule-interval: 30   # 30 秒執行一次（測試環境更頻繁）
      batch-size: 100         # 測試環境較小的批次大小
      expired-key-scan-count: 50

# 日誌配置 - 測試環境
logging:
  level:
    com.nanshan: DEBUG
    org.redisson: INFO
    org.springframework.data.redis: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
