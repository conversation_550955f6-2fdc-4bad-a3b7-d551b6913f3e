<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.nanshan.common.cache.service.CacheManagerTest" time="3.101" tests="11" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="MS950"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\workspace\common-base\common-cache\target\test-classes;C:\Users\<USER>\Desktop\workspace\common-base\common-cache\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-processor\3.2.0\spring-boot-configuration-processor-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson-spring-boot-starter\3.24.3\redisson-spring-boot-starter-3.24.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.0\spring-boot-starter-actuator-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.0\spring-boot-actuator-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.2.0\spring-boot-actuator-3.2.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.12.0\micrometer-jakarta9-1.12.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.12.0\micrometer-core-1.12.0.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\3.2.0\spring-boot-starter-data-redis-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\3.2.0\spring-data-redis-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\3.2.0\spring-data-keyvalue-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.2.0\spring-data-commons-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.1.1\spring-tx-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\6.1.1\spring-oxm-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.1.1\spring-context-support-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson\3.24.3\redisson-3.24.3.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.101.Final\netty-resolver-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.101.Final\netty-codec-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.6.0\reactor-core-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava3\rxjava\3.1.8\rxjava-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling\2.0.11.Final\jboss-marshalling-2.0.11.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling-river\2.0.11.Final\jboss-marshalling-river-2.0.11.Final.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\kryo\5.5.0\kryo-5.5.0.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\reflectasm\1.11.9\reflectasm-1.11.9.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\minlog\1.3.1\minlog-1.3.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.3\jackson-dataformat-yaml-2.15.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-bean\5.1.6\jodd-bean-5.1.6.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-core\5.1.6\jodd-core-5.1.6.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson-spring-data-31\3.24.3\redisson-spring-data-31-3.24.3.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.34\lombok-1.18.34.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.3\asm-9.3.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\Users\<USER>\.m2\repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Taipei"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="TW"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-21\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire3091040923826096610\surefirebooter-20250730112037088_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire3091040923826096610 2025-07-30T11-20-36_603-jvmRun1 surefire-20250730112037088_1tmp surefire_0-20250730112037088_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\workspace\common-base\common-cache\target\test-classes;C:\Users\<USER>\Desktop\workspace\common-base\common-cache\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-processor\3.2.0\spring-boot-configuration-processor-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson-spring-boot-starter\3.24.3\redisson-spring-boot-starter-3.24.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.0\spring-boot-starter-actuator-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.0\spring-boot-actuator-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.2.0\spring-boot-actuator-3.2.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.12.0\micrometer-jakarta9-1.12.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.12.0\micrometer-core-1.12.0.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\3.2.0\spring-boot-starter-data-redis-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\3.2.0\spring-data-redis-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\3.2.0\spring-data-keyvalue-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.2.0\spring-data-commons-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.1.1\spring-tx-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\6.1.1\spring-oxm-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.1.1\spring-context-support-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson\3.24.3\redisson-3.24.3.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.101.Final\netty-resolver-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.101.Final\netty-codec-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.6.0\reactor-core-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava3\rxjava\3.1.8\rxjava-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling\2.0.11.Final\jboss-marshalling-2.0.11.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling-river\2.0.11.Final\jboss-marshalling-river-2.0.11.Final.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\kryo\5.5.0\kryo-5.5.0.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\reflectasm\1.11.9\reflectasm-1.11.9.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\minlog\1.3.1\minlog-1.3.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.3\jackson-dataformat-yaml-2.15.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-bean\5.1.6\jodd-bean-5.1.6.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-core\5.1.6\jodd-core-5.1.6.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson-spring-data-31\3.24.3\redisson-spring-data-31-3.24.3.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.34\lombok-1.18.34.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.3\asm-9.3.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\Users\<USER>\.m2\repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-21"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\workspace\common-base\common-cache"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire3091040923826096610\surefirebooter-20250730112037088_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.7+8-LTS-245"/>
    <property name="user.name" value="Nofi"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\workspace\common-base\common-cache"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="39888"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="MS950"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\pleiades.java-extension-pack-jdk\gradle\latest\bin;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\pleiades.java-extension-pack-jdk\maven\latest\bin;C:\Program Files\Java\jdk-21\bin;C:\Python313\Scripts\;C:\Python313\;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;C:\Windows\System32\curl.exe;C:\Windows\System32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\dotnet\;C:\Program Files\Java\jdk-21\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\apache-maven-3.9.10\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.1\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.7+8-LTS-245"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>
    <property name="LOGGED_APPLICATION_NAME" value="[common-cache] "/>
  </properties>
  <testcase name="testGetStats" classname="com.nanshan.common.cache.service.CacheManagerTest" time="0.042">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-30 11:20:47.992 [main] INFO  c.n.c.cache.service.CacheManagerTest - Starting CacheManagerTest using Java 21.0.7 with PID 39888 (started by Nofi in C:\Users\<USER>\Desktop\workspace\common-base\common-cache)
2025-07-30 11:20:47.992 [main] DEBUG c.n.c.cache.service.CacheManagerTest - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-07-30 11:20:47.993 [main] INFO  c.n.c.cache.service.CacheManagerTest - No active profile set, falling back to 1 default profile: "default"
2025-07-30 11:20:48.234 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 11:20:48.235 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 11:20:48.244 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-07-30 11:20:48.279 [main] INFO  org.redisson.Version - Redisson 3.24.3
2025-07-30 11:20:48.349 [redisson-netty-5-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for localhost/127.0.0.1:6379
2025-07-30 11:20:48.529 [redisson-netty-5-20] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for localhost/127.0.0.1:6379
2025-07-30 11:20:48.551 [main] INFO  c.n.c.c.s.impl.SessionManagerImpl - SessionManager Redisson optimizations initialized successfully
2025-07-30 11:20:48.562 [main] INFO  c.n.c.c.s.RedisConnectionManager - Redis connection test successful
2025-07-30 11:20:48.562 [main] INFO  c.n.c.c.s.RedisConnectionManager - Redis connection manager initialized successfully
2025-07-30 11:20:48.668 [main] INFO  c.n.c.cache.service.CacheManagerTest - Started CacheManagerTest in 0.72 seconds (process running for 11.394)
2025-07-30 11:20:48.668 [scheduling-1] DEBUG c.n.c.c.s.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-30 11:20:48.671 [scheduling-1] DEBUG c.n.c.c.s.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-30 11:20:48.671 [scheduling-1] DEBUG c.n.c.c.s.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-30 11:20:48.672 [scheduling-1] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 11:20:48.672 [scheduling-1] DEBUG c.n.c.c.s.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-30 11:20:48.675 [scheduling-1] DEBUG c.n.c.c.s.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-30 11:20:48.675 [scheduling-1] INFO  c.n.c.c.s.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-30 11:20:48.686 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:48.689 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=stats1, ttl=60s
2025-07-30 11:20:48.693 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=stats2, ttl=60s
2025-07-30 11:20:48.699 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=stats1
2025-07-30 11:20:48.701 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=stats2
2025-07-30 11:20:48.709 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
]]></system-out>
  </testcase>
  <testcase name="testPutWithDuration" classname="com.nanshan.common.cache.service.CacheManagerTest" time="0.016">
    <system-out><![CDATA[2025-07-30 11:20:48.718 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:48.720 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=123, ttl=120s
2025-07-30 11:20:48.728 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=123
]]></system-out>
  </testcase>
  <testcase name="testPutWithDefaultTTL" classname="com.nanshan.common.cache.service.CacheManagerTest" time="0.015">
    <system-out><![CDATA[2025-07-30 11:20:48.738 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:48.740 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=123, ttl=60s
2025-07-30 11:20:48.747 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=123
]]></system-out>
  </testcase>
  <testcase name="testMultiRemove" classname="com.nanshan.common.cache.service.CacheManagerTest" time="0.036">
    <system-out><![CDATA[2025-07-30 11:20:48.753 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:48.759 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=key1, ttl=60s
2025-07-30 11:20:48.763 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=key2, ttl=60s
2025-07-30 11:20:48.764 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=key3, ttl=60s
2025-07-30 11:20:48.773 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=key1
2025-07-30 11:20:48.775 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=key2
2025-07-30 11:20:48.778 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=key3
2025-07-30 11:20:48.784 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
]]></system-out>
  </testcase>
  <testcase name="testPutAndGet" classname="com.nanshan.common.cache.service.CacheManagerTest" time="0.012">
    <system-out><![CDATA[2025-07-30 11:20:48.789 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:48.791 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=123, ttl=60s
2025-07-30 11:20:48.795 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache hit: type=test, id=123
2025-07-30 11:20:48.797 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=123
]]></system-out>
  </testcase>
  <testcase name="testCleanupExpiredCaches" classname="com.nanshan.common.cache.service.CacheManagerTest" time="2.025">
    <system-out><![CDATA[2025-07-30 11:20:48.802 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:48.805 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=short1, ttl=1s
2025-07-30 11:20:48.806 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=short2, ttl=1s
2025-07-30 11:20:50.819 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-30 11:20:50.821 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
]]></system-out>
  </testcase>
  <testcase name="testMultiGet" classname="com.nanshan.common.cache.service.CacheManagerTest" time="0.053">
    <system-out><![CDATA[2025-07-30 11:20:50.833 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:50.836 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=key1, ttl=60s
2025-07-30 11:20:50.838 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=key2, ttl=60s
2025-07-30 11:20:50.840 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=key3, ttl=60s
2025-07-30 11:20:50.843 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache hit: type=test, id=key1
2025-07-30 11:20:50.849 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache hit: type=test, id=key2
2025-07-30 11:20:50.855 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache hit: type=test, id=key3
2025-07-30 11:20:50.864 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=key1
2025-07-30 11:20:50.867 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=key2
2025-07-30 11:20:50.871 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=key3
2025-07-30 11:20:50.875 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
]]></system-out>
  </testcase>
  <testcase name="testMultiPut" classname="com.nanshan.common.cache.service.CacheManagerTest" time="0.055">
    <system-out><![CDATA[2025-07-30 11:20:50.893 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:50.899 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=key1, ttl=60s
2025-07-30 11:20:50.902 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=key2, ttl=60s
2025-07-30 11:20:50.905 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=key3, ttl=60s
2025-07-30 11:20:50.915 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache hit: type=test, id=key1
2025-07-30 11:20:50.919 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache hit: type=test, id=key2
2025-07-30 11:20:50.924 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache hit: type=test, id=key3
2025-07-30 11:20:50.930 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=key1
2025-07-30 11:20:50.933 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=key2
2025-07-30 11:20:50.935 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=key3
2025-07-30 11:20:50.938 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
]]></system-out>
  </testcase>
  <testcase name="testRemove" classname="com.nanshan.common.cache.service.CacheManagerTest" time="0.026">
    <system-out><![CDATA[2025-07-30 11:20:50.950 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:50.953 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=123, ttl=60s
2025-07-30 11:20:50.960 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=123
2025-07-30 11:20:50.964 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:50.967 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
]]></system-out>
  </testcase>
  <testcase name="testGetOrCompute" classname="com.nanshan.common.cache.service.CacheManagerTest" time="0.027">
    <system-out><![CDATA[2025-07-30 11:20:50.981 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:50.984 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=123, ttl=60s
2025-07-30 11:20:50.990 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache hit: type=test, id=123
2025-07-30 11:20:50.995 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache hit: type=test, id=123
2025-07-30 11:20:50.998 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=123
]]></system-out>
  </testcase>
  <testcase name="testRenewCache" classname="com.nanshan.common.cache.service.CacheManagerTest" time="0.031">
    <system-out><![CDATA[2025-07-30 11:20:51.008 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache not found for removal: type=test, id=123
2025-07-30 11:20:51.015 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache stored: type=test, id=123, ttl=30s
2025-07-30 11:20:51.019 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache renewed: type=test, id=123, ttl=60s
2025-07-30 11:20:51.029 [main] DEBUG c.n.c.c.s.impl.CacheManagerImpl - Cache removed: type=test, id=123
]]></system-out>
  </testcase>
</testsuite>