package com.nanshan.demo.config;

import com.nanshan.common.cache.config.CommonCacheProperties;
import com.nanshan.demo.cache.DemoCommonCacheApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Redis 配置驗證測試
 *
 * 驗證 Demo 專案中的 Redis 配置是否正確載入和應用
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = DemoCommonCacheApplication.class)
public class RedisConfigurationValidationTest {

    @Autowired
    private RedisProperties redisProperties;

    @Autowired
    private CommonCacheProperties cacheProperties;

    @Test
    public void testDefaultRedisConfiguration() {
        // 驗證 Spring Redis 基本配置
        assertThat(redisProperties.getHost()).isEqualTo("localhost");
        assertThat(redisProperties.getPort()).isEqualTo(6379);
        assertThat(redisProperties.getDatabase()).isEqualTo(0);
        assertThat(redisProperties.getTimeout().toMillis()).isEqualTo(3000);

        // 用戶名和密碼在預設配置中為空
        assertThat(redisProperties.getUsername()).isNullOrEmpty();
        assertThat(redisProperties.getPassword()).isNullOrEmpty();
    }

    @Test
    public void testCommonCacheRedisConfiguration() {
        var redisConfig = cacheProperties.getRedis();

        // 驗證連線池配置
        var connectionPool = redisConfig.getConnectionPool();
        assertThat(connectionPool.getPoolSize()).isEqualTo(32);
        assertThat(connectionPool.getMinimumIdleSize()).isEqualTo(5);
        assertThat(connectionPool.getIdleConnectionTimeout()).isEqualTo(10000);

        // 驗證超時配置
        var timeout = redisConfig.getTimeout();
        assertThat(timeout.getConnectTimeout()).isEqualTo(10000);
        assertThat(timeout.getCommandTimeout()).isEqualTo(3000);

        // 驗證重試配置
        var retry = redisConfig.getRetry();
        assertThat(retry.getAttempts()).isEqualTo(3);
        assertThat(retry.getInterval()).isEqualTo(1500);

        // 驗證執行緒池配置
        var threadPool = redisConfig.getThreadPool();
        assertThat(threadPool.getThreads()).isEqualTo(8);
        assertThat(threadPool.getNettyThreads()).isEqualTo(16);

        // 驗證其他配置
        var misc = redisConfig.getMisc();
        assertThat(misc.isKeepAlive()).isTrue();
        assertThat(misc.isTcpNoDelay()).isTrue();
    }

    @Test
    public void testCommonCacheOtherConfiguration() {
        // 驗證環境保護配置
        var envGuard = cacheProperties.getEnvGuard();
        assertThat(envGuard.isEnabled()).isTrue();
        assertThat(envGuard.isDangerousOperationsEnabled()).isTrue(); // Demo 環境允許危險操作

        // 驗證 Session 配置
        var session = cacheProperties.getSession();
        assertThat(session.getDefaultTimeToLive()).isEqualTo(1800);
        assertThat(session.getKeyPrefix()).isEqualTo("demo:session");

        // 驗證快取配置
        var cache = cacheProperties.getCache();
        assertThat(cache.getDefaultTimeToLive()).isEqualTo(3600);
        assertThat(cache.getKeyPrefix()).isEqualTo("demo:cache");

        // 驗證鎖配置
        var lock = cacheProperties.getLock();
        assertThat(lock.getDefaultLeaseTime()).isEqualTo(30);
        assertThat(lock.getKeyPrefix()).isEqualTo("demo:lock");

        // 驗證清理器配置
        var cleaner = cacheProperties.getCleaner();
        assertThat(cleaner.isEnabled()).isTrue();
        assertThat(cleaner.getScheduleInterval()).isEqualTo(300); // Demo 環境更頻繁清理
    }

    /**
     * 開發環境配置測試
     */
    @SpringBootTest(classes = DemoCommonCacheApplication.class)
    @ActiveProfiles("dev")
    static class DevEnvironmentTest {

        @Autowired
        private RedisProperties redisProperties;

        @Autowired
        private CommonCacheProperties cacheProperties;

        @Test
        public void testDevRedisConfiguration() {
            // 驗證開發環境 Redis 配置
            assertThat(redisProperties.getHost()).isEqualTo("localhost");
            assertThat(redisProperties.getPort()).isEqualTo(6379);
            assertThat(redisProperties.getDatabase()).isEqualTo(1); // 開發環境使用不同資料庫
            assertThat(redisProperties.getTimeout().toMillis()).isEqualTo(2000);
        }

        @Test
        public void testDevCommonCacheConfiguration() {
            var redisConfig = cacheProperties.getRedis();

            // 驗證開發環境的連線池配置
            assertThat(redisConfig.getConnectionPool().getPoolSize()).isEqualTo(16);
            assertThat(redisConfig.getThreadPool().getThreads()).isEqualTo(4);

            // 驗證開發環境的 Session 配置
            var session = cacheProperties.getSession();
            assertThat(session.getDefaultTimeToLive()).isEqualTo(600); // 開發環境較短 TTL
            assertThat(session.getKeyPrefix()).isEqualTo("dev:session");
        }
    }

    /**
     * 生產環境配置測試
     */
    @SpringBootTest(classes = DemoCommonCacheApplication.class)
    @ActiveProfiles("prod")
    static class ProdEnvironmentTest {

        @Autowired
        private CommonCacheProperties cacheProperties;

        @Test
        public void testProdCommonCacheConfiguration() {
            var redisConfig = cacheProperties.getRedis();

            // 驗證生產環境的連線池配置
            assertThat(redisConfig.getConnectionPool().getPoolSize()).isEqualTo(64);
            assertThat(redisConfig.getThreadPool().getThreads()).isEqualTo(16);

            // 驗證生產環境的安全配置
            var envGuard = cacheProperties.getEnvGuard();
            assertThat(envGuard.isDangerousOperationsEnabled()).isFalse(); // 生產環境禁止危險操作

            // 驗證生產環境的 Session 配置
            var session = cacheProperties.getSession();
            assertThat(session.getDefaultTimeToLive()).isEqualTo(3600); // 生產環境較長 TTL
            assertThat(session.getKeyPrefix()).isEqualTo("prod:session");
        }
    }
}
