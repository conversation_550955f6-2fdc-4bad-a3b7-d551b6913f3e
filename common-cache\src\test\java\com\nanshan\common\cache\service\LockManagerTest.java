package com.nanshan.common.cache.service;

import com.nanshan.common.cache.annotation.EnableRedisSupport;
import com.nanshan.common.cache.config.TestConfig;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * LockManager 測試
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = LockManagerTest.LockTestApplication.class)
@Import(TestConfig.class)
@TestPropertySource(properties = {
    "common.cache.lock.default-lease-time=30",
    "common.cache.lock.default-wait-time=10",
    "common.cache.lock.fair-lock-enabled=true"
})
public class LockManagerTest {

    @SpringBootApplication
    @EnableRedisSupport
    @Import(TestConfig.class)
    public static class LockTestApplication {

        public static void main(String[] args) {
            SpringApplication.run(LockTestApplication.class, args);
        }
    }

    @Autowired
    private LockManager lockManager;

    private final String testResource = "test-resource";

    @BeforeEach
    void setUp() {
        // 確保測試開始前沒有鎖
        if (lockManager.isLocked(testResource)) {
            lockManager.forceUnlock(testResource);
        }
    }

    @AfterEach
    void tearDown() {
        // 清理測試鎖
        if (lockManager.isLocked(testResource)) {
            lockManager.forceUnlock(testResource);
        }
    }

    @Test
    void testTryLock() {
        // 獲取鎖
        boolean locked = lockManager.tryLock(testResource, 5, 30, TimeUnit.SECONDS);
        assertTrue(locked);

        // 檢查鎖狀態
        assertTrue(lockManager.isLocked(testResource));
        assertTrue(lockManager.isHeldByCurrentThread(testResource));

        // 釋放鎖
        boolean unlocked = lockManager.unlock(testResource);
        assertTrue(unlocked);
        assertFalse(lockManager.isLocked(testResource));
    }

    @Test
    void testTryLockWithDefaultSettings() {
        // 使用預設設定獲取鎖
        boolean locked = lockManager.tryLock(testResource);
        assertTrue(locked);

        // 檢查鎖狀態
        assertTrue(lockManager.isLocked(testResource));
        assertTrue(lockManager.isHeldByCurrentThread(testResource));

        // 釋放鎖
        boolean unlocked = lockManager.unlock(testResource);
        assertTrue(unlocked);
    }

    @Test
    void testTryLockWithDuration() {
        // 使用 Duration 獲取鎖
        boolean locked = lockManager.tryLock(testResource, Duration.ofSeconds(5), Duration.ofSeconds(30));
        assertTrue(locked);

        // 檢查鎖狀態
        assertTrue(lockManager.isLocked(testResource));

        // 釋放鎖
        boolean unlocked = lockManager.unlock(testResource);
        assertTrue(unlocked);
    }

    @Test
    void testLockTimeout() throws InterruptedException, ExecutionException, TimeoutException {
        String timeoutResource = testResource + "-timeout";

        // 第一個執行緒獲取鎖
        boolean firstLocked = lockManager.tryLock(timeoutResource, 1, 30, TimeUnit.SECONDS);
        assertTrue(firstLocked);

        // 使用 CompletableFuture 在不同執行緒中測試超時
        CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            boolean secondLocked = lockManager.tryLock(timeoutResource, 1, 30, TimeUnit.SECONDS);
            long endTime = System.currentTimeMillis();

            // 驗證等待時間
            assertTrue(endTime - startTime >= 1000, "Should wait at least 1 second");

            return secondLocked;
        });

        // 等待結果
        Boolean secondLocked = future.get(5, TimeUnit.SECONDS);
        assertFalse(secondLocked, "Second lock should fail due to timeout");

        // 釋放第一個鎖
        lockManager.unlock(timeoutResource);
    }

    @Test
    void testExecuteWithLock() {
        AtomicInteger counter = new AtomicInteger(0);

        String result;
        try {
            boolean locked = lockManager.tryLock(testResource, 5, 30, TimeUnit.SECONDS);
            assertTrue(locked);

            // 在鎖保護下執行的操作
            assertTrue(lockManager.isHeldByCurrentThread(testResource));
            counter.incrementAndGet();
            result = "success";
        } finally {
            lockManager.unlock(testResource);
        }

        assertEquals("success", result);
        assertEquals(1, counter.get());
        assertFalse(lockManager.isLocked(testResource));
    }

    @Test
    void testExecuteWithLockException() {
        AtomicInteger counter = new AtomicInteger(0);

        assertThrows(RuntimeException.class, () -> {
            try {
                boolean locked = lockManager.tryLock(testResource, 5, 30, TimeUnit.SECONDS);
                assertTrue(locked);

                counter.incrementAndGet();
                throw new RuntimeException("Test exception");
            } finally {
                lockManager.unlock(testResource);
            }
        });

        assertEquals(1, counter.get());
        assertFalse(lockManager.isLocked(testResource)); // 鎖應該被釋放
    }

    @Test
    void testConcurrentLocking() throws Exception {
        AtomicInteger counter = new AtomicInteger(0);
        int threadCount = 5;

        CompletableFuture<String>[] futures = new CompletableFuture[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int taskId = i + 1;
            futures[i] = CompletableFuture.supplyAsync(() -> {
                try {
                    boolean locked = lockManager.tryLock(testResource, 10, 30, TimeUnit.SECONDS);
                    if (!locked) {
                        return "Task " + taskId + " failed to acquire lock";
                    }

                    // 模擬業務處理時間
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }

                    int currentValue = counter.incrementAndGet();
                    return "Task " + taskId + " completed, counter: " + currentValue;
                } finally {
                    if (lockManager.isHeldByCurrentThread(testResource)) {
                        lockManager.unlock(testResource);
                    }
                }
            });
        }

        // 等待所有任務完成
        CompletableFuture.allOf(futures).join();

        // 驗證計數器值
        assertEquals(threadCount, counter.get());

        // 驗證所有任務都成功完成
        for (CompletableFuture<String> future : futures) {
            String result = future.get();
            assertNotNull(result);
            assertTrue(result.startsWith("Task"));
        }

        // 驗證鎖已釋放
        assertFalse(lockManager.isLocked(testResource));
    }

    @Test
    void testForceUnlock() {
        // 獲取鎖
        boolean locked = lockManager.tryLock(testResource, 5, 30, TimeUnit.SECONDS);
        assertTrue(locked);
        assertTrue(lockManager.isLocked(testResource));

        // 強制釋放鎖
        boolean forceUnlocked = lockManager.forceUnlock(testResource);
        assertTrue(forceUnlocked);
        assertFalse(lockManager.isLocked(testResource));

        // 再次強制釋放應該返回 false
        boolean forceUnlockedAgain = lockManager.forceUnlock(testResource);
        assertFalse(forceUnlockedAgain);
    }

    @Test
    void testGetRemainingLeaseTime() {
        // 獲取鎖並設定租約時間
        boolean locked = lockManager.tryLock(testResource, 1, 30, TimeUnit.SECONDS);
        assertTrue(locked);

        // 檢查剩餘租約時間
        long remainingTime = lockManager.getRemainingLeaseTime(testResource, TimeUnit.SECONDS);
        assertTrue(remainingTime > 0 && remainingTime <= 30);

        // 釋放鎖
        lockManager.unlock(testResource);

        // 檢查鎖釋放後的剩餘時間
        long remainingTimeAfterUnlock = lockManager.getRemainingLeaseTime(testResource, TimeUnit.SECONDS);
        assertEquals(-2, remainingTimeAfterUnlock); // 鎖不存在
    }

    @Test
    void testReadWriteLock() {
        String readResource = testResource + "-read";
        String writeResource = testResource + "-write";

        // 測試讀鎖
        boolean readLocked = lockManager.tryReadLock(readResource, 5, 30, TimeUnit.SECONDS);
        assertTrue(readLocked);

        // 釋放讀鎖
        boolean readUnlocked = lockManager.unlock(readResource);
        assertTrue(readUnlocked);

        // 測試寫鎖
        boolean writeLocked = lockManager.tryWriteLock(writeResource, 5, 30, TimeUnit.SECONDS);
        assertTrue(writeLocked);

        // 釋放寫鎖
        boolean writeUnlocked = lockManager.unlock(writeResource);
        assertTrue(writeUnlocked);
    }

    @Test
    void testFairLock() {
        String fairResource = testResource + "-fair";

        // 測試公平鎖
        boolean fairLocked = lockManager.tryFairLock(fairResource, 5, 30, TimeUnit.SECONDS);
        assertTrue(fairLocked);

        // 釋放公平鎖
        boolean fairUnlocked = lockManager.unlock(fairResource);
        assertTrue(fairUnlocked);

        // 釋放後應該能夠再次獲取
        boolean fairLocked2 = lockManager.tryFairLock(fairResource, 1, 30, TimeUnit.SECONDS);
        assertTrue(fairLocked2);

        // 清理
        lockManager.unlock(fairResource);
    }

    // 移除 testMultiLock 測試，因為 LockManager 介面中沒有多重鎖方法
}
