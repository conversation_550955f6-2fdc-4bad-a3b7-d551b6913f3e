package com.nanshan.common.cache.service;

import com.nanshan.common.cache.annotation.EnableRedisSupport;
import com.nanshan.common.cache.config.TestConfig;
import com.nanshan.common.cache.model.SessionObject;
import com.nanshan.common.cache.model.SessionObjectBuilder;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * RedisCleaner 測試
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = RedisCleanerTest.CleanerTestApplication.class)
@Import(TestConfig.class)
@TestPropertySource(properties = {
    "common.cache.cleaner.enabled=true",
    "common.cache.cleaner.schedule-interval=30",
    "common.cache.cleaner.batch-size=100",
    "common.cache.cleaner.expired-key-scan-count=50",
    "common.cache.cleaner.statistics-enabled=true"
})
public class RedisCleanerTest {

    @SpringBootApplication
    @EnableRedisSupport
    @Import(TestConfig.class)
    public static class CleanerTestApplication {

        public static void main(String[] args) {
            SpringApplication.run(CleanerTestApplication.class, args);
        }
    }

    @Autowired
    private RedisCleaner redisCleaner;

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private LockManager lockManager;

    @BeforeEach
    void setUp() {
        // 清理測試環境
        cleanupTestData();
    }

    @AfterEach
    void tearDown() {
        // 清理測試資料
        cleanupTestData();
    }

    private void cleanupTestData() {
        // 清理測試 Session
        sessionManager.deleteSession("cleaner-test-jwt-1");
        sessionManager.deleteSession("cleaner-test-jwt-2");
        sessionManager.deleteSession("cleaner-test-jwt-3");

        // 清理測試快取
        cacheManager.remove("cleaner-test", "cache1");
        cacheManager.remove("cleaner-test", "cache2");
        cacheManager.remove("cleaner-test", "cache3");

        // 清理測試鎖
        if (lockManager.isLocked("cleaner-test-lock-1")) {
            lockManager.forceUnlock("cleaner-test-lock-1");
        }
        if (lockManager.isLocked("cleaner-test-lock-2")) {
            lockManager.forceUnlock("cleaner-test-lock-2");
        }
    }

    @Test
    void testPerformManualCleanup() {
        // 建立一些測試資料
        createTestData();

        // 執行手動清理
        RedisCleaner.CleanupResult result = redisCleaner.performManualCleanup();

        // 驗證清理結果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getDurationMs() >= 0);
        assertTrue(result.getSessionsCleaned() >= 0);
        assertTrue(result.getCachesCleaned() >= 0);
        assertTrue(result.getLocksCleaned() >= 0);
    }

    @Test
    void testCleanupExpiredSessions() {
        // 建立一些短期 Session
        SessionObject session1 = SessionObjectBuilder.createBasicSession("user1", "client1", "cleaner-test-jwt-1");
        SessionObject session2 = SessionObjectBuilder.createBasicSession("user2", "client2", "cleaner-test-jwt-2");

        sessionManager.saveSession("cleaner-test-jwt-1", session1, 1); // 1 秒過期
        sessionManager.saveSession("cleaner-test-jwt-2", session2, 1); // 1 秒過期

        // 等待過期
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 執行 Session 清理
        long cleanedCount = redisCleaner.cleanupExpiredSessions();
        assertTrue(cleanedCount >= 0);
    }

    @Test
    void testCleanupExpiredCaches() {
        // 建立一些短期快取
        cacheManager.put("cleaner-test", "cache1", "value1", 1, TimeUnit.SECONDS);
        cacheManager.put("cleaner-test", "cache2", "value2", 1, TimeUnit.SECONDS);

        // 等待過期
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 執行快取清理
        long cleanedCount = redisCleaner.cleanupExpiredCaches();
        assertTrue(cleanedCount >= 0);
    }

    @Test
    void testCleanupExpiredLocks() {
        // 建立一些短期鎖
        lockManager.tryLock("cleaner-test-lock-1", 1, 1, TimeUnit.SECONDS);
        lockManager.tryLock("cleaner-test-lock-2", 1, 1, TimeUnit.SECONDS);

        // 等待過期
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 執行鎖清理
        long cleanedCount = redisCleaner.cleanupExpiredLocks();
        assertTrue(cleanedCount >= 0);
    }

    @Test
    void testGetCleanupStats() {
        // 執行一次清理以產生統計資料
        redisCleaner.performManualCleanup();

        // 獲取統計資訊
        RedisCleaner.CleanupStats stats = redisCleaner.getCleanupStats();
        assertNotNull(stats);
        assertTrue(stats.getTotalCleanups() >= 1);
        assertTrue(stats.getTotalSessionsCleaned() >= 0);
        assertTrue(stats.getTotalCachesCleaned() >= 0);
        assertTrue(stats.getTotalLocksCleaned() >= 0);
        assertTrue(stats.getAverageCleanupDuration() >= 0);
        assertNotNull(stats.getLastCleanupTime());
    }

    @Test
    void testDeleteByPattern() {
        // 建立一些測試資料
        cacheManager.put("pattern-test", "item1", "value1", 60, TimeUnit.SECONDS);
        cacheManager.put("pattern-test", "item2", "value2", 60, TimeUnit.SECONDS);
        cacheManager.put("pattern-test", "item3", "value3", 60, TimeUnit.SECONDS);
        cacheManager.put("other-test", "item1", "value1", 60, TimeUnit.SECONDS);

        // 驗證資料存在
        assertTrue(cacheManager.exists("pattern-test", "item1"));
        assertTrue(cacheManager.exists("pattern-test", "item2"));
        assertTrue(cacheManager.exists("pattern-test", "item3"));
        assertTrue(cacheManager.exists("other-test", "item1"));

        // 使用模式刪除
        long deletedCount = redisCleaner.deleteByPattern("cache:pattern-test:*");
        assertTrue(deletedCount >= 3);

        // 驗證模式匹配的資料已刪除
        assertFalse(cacheManager.exists("pattern-test", "item1"));
        assertFalse(cacheManager.exists("pattern-test", "item2"));
        assertFalse(cacheManager.exists("pattern-test", "item3"));

        // 驗證其他資料仍然存在
        assertTrue(cacheManager.exists("other-test", "item1"));

        // 清理剩餘資料
        cacheManager.remove("other-test", "item1");
    }

    @Test
    void testCleanupWithBatchSize() {
        // 建立大量測試資料
        for (int i = 1; i <= 10; i++) {
            cacheManager.put("batch-test", "item" + i, "value" + i, 1, TimeUnit.SECONDS);
        }

        // 等待過期
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 執行清理
        RedisCleaner.CleanupResult result = redisCleaner.performManualCleanup();
        assertNotNull(result);
        assertTrue(result.isSuccess());
    }

    // 移除不存在的方法測試
    private void createTestData() {
        // 建立測試 Session
        SessionObject session = SessionObjectBuilder.createBasicSession("test-user", "test-client", "cleaner-test-jwt-3");
        sessionManager.saveSession("cleaner-test-jwt-3", session, 60);

        // 建立測試快取
        cacheManager.put("cleaner-test", "cache3", "value3", 60, TimeUnit.SECONDS);

        // 建立測試鎖（短期）
        lockManager.tryLock("cleaner-test-lock-3", 1, 5, TimeUnit.SECONDS);
    }

    @Test
    void testCleanupResultProperties() {
        // 執行清理
        RedisCleaner.CleanupResult result = redisCleaner.performManualCleanup();

        // 驗證結果屬性
        assertNotNull(result);
        assertTrue(result.getDurationMs() >= 0);

        // 驗證計數屬性
        assertTrue(result.getSessionsCleaned() >= 0);
        assertTrue(result.getCachesCleaned() >= 0);
        assertTrue(result.getLocksCleaned() >= 0);

        // 驗證成功狀態
        assertTrue(result.isSuccess());
    }

    @Test
    void testCleanupStatsProperties() {
        // 執行幾次清理以產生統計資料
        redisCleaner.performManualCleanup();
        redisCleaner.performManualCleanup();

        // 獲取統計資訊
        RedisCleaner.CleanupStats stats = redisCleaner.getCleanupStats();

        // 驗證統計屬性
        assertNotNull(stats);
        assertTrue(stats.getTotalCleanups() >= 2);
        assertTrue(stats.getTotalSessionsCleaned() >= 0);
        assertTrue(stats.getTotalCachesCleaned() >= 0);
        assertTrue(stats.getTotalLocksCleaned() >= 0);
        assertTrue(stats.getAverageCleanupDuration() >= 0);
        assertNotNull(stats.getLastCleanupTime());
    }
}
