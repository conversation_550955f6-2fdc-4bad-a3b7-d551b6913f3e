# Redisson DEBUG 日誌問題修復指南

## 🔍 **問題描述**

在運行 `mvn spring-boot:run` 後，控制台每隔幾秒會輸出大量的 Redisson DEBUG 日誌：

```
2025-07-22 13:10:51.397 [redisson-netty-2-1] DEBUG org.redisson.command.RedisExecutor - 
acquired connection for command (EVAL) and params [if redis.call('setnx', KEYS[6], ARGV[4]) == 0 then return -1;end;redis.call('expire', KEYS[6], ARGV[3]); local expiredKeys1 = redis.call('zrangebyscore', KEYS[2], 0, ARGV[1], 'limit', 0, ARGV[2]); ...
```

## 🔧 **問題原因**

1. **Redisson 內建清理機制**: Redisson 會自動清理過期的快取項目
2. **DEBUG 級別日誌**: 預設日誌級別顯示了 Redisson 的內部操作
3. **正常運行機制**: 這是 Redisson 的正常工作流程，但日誌輸出過於詳細

## ✅ **解決方案**

### **1. 創建專門的 Logback 配置**

我們創建了 `logback-spring.xml` 文件來精確控制日誌輸出：

```xml
<!-- Redisson 命令執行日誌 - 完全關閉 DEBUG -->
<logger name="org.redisson.command" level="ERROR" additivity="false">
    <appender-ref ref="FILE"/>
</logger>

<!-- Redisson 日誌控制 -->
<logger name="org.redisson" level="WARN" additivity="false">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="FILE"/>
</logger>
```

### **2. 關鍵配置說明**

| Logger 名稱 | 級別 | 說明 |
|-------------|------|------|
| `org.redisson.command` | ERROR | 完全關閉命令執行的 DEBUG 日誌 |
| `org.redisson` | WARN | 只顯示警告和錯誤 |
| `org.redisson.connection` | WARN | 連線相關日誌 |
| `io.lettuce.core` | WARN | Redis 客戶端日誌 |
| `io.netty` | WARN | 網路層日誌 |

### **3. 環境特定配置**

- **開發環境**: 應用程式日誌為 DEBUG，Redisson 為 WARN
- **測試環境**: 應用程式日誌為 DEBUG，Redisson 為 INFO
- **生產環境**: 所有日誌為 WARN 或更高級別

## 🚀 **驗證修復**

### **1. 重新啟動應用**

```bash
mvn spring-boot:run
```

### **2. 預期結果**

- ✅ 不再看到 Redisson 的 DEBUG 日誌
- ✅ 應用程式日誌正常顯示
- ✅ 只有重要的警告和錯誤會顯示

### **3. 測試腳本**

```bash
# Windows
scripts\test-logging.bat

# Linux/Mac
scripts/test-logging.sh
```

## 📊 **日誌級別對照表**

| 級別 | 說明 | 使用場景 |
|------|------|----------|
| **ERROR** | 錯誤信息 | 系統錯誤、異常 |
| **WARN** | 警告信息 | 潛在問題、配置警告 |
| **INFO** | 一般信息 | 應用程式狀態、重要操作 |
| **DEBUG** | 調試信息 | 開發調試、詳細追蹤 |
| **TRACE** | 追蹤信息 | 最詳細的日誌 |

## 🔧 **進階配置**

### **1. 臨時調整日誌級別**

如果需要臨時查看 Redisson 的詳細日誌：

```bash
# 啟動時指定日誌級別
mvn spring-boot:run -Dlogging.level.org.redisson=DEBUG
```

### **2. 運行時調整日誌級別**

通過 Actuator 端點動態調整：

```bash
# 查看當前日誌級別
curl http://localhost:8083/actuator/loggers/org.redisson

# 動態調整日誌級別
curl -X POST http://localhost:8083/actuator/loggers/org.redisson \
  -H "Content-Type: application/json" \
  -d '{"configuredLevel": "WARN"}'
```

### **3. 自定義日誌輸出**

如果需要自定義日誌格式：

```xml
<property name="CUSTOM_PATTERN" 
          value="%d{HH:mm:ss} [%thread] %-5level %logger{20} - %msg%n"/>
```

## 📝 **最佳實踐**

### **1. 開發環境**
- 應用程式日誌: DEBUG
- 框架日誌: INFO 或 WARN
- 第三方庫: WARN

### **2. 生產環境**
- 應用程式日誌: INFO
- 框架日誌: WARN
- 第三方庫: ERROR

### **3. 日誌文件管理**
- 使用滾動日誌文件
- 設定合適的保留期限
- 監控日誌文件大小

## 🔍 **故障排除**

### **問題**: 修改後仍然看到 DEBUG 日誌

**解決方案**:
1. 確認 `logback-spring.xml` 文件位置正確
2. 重新啟動應用程式
3. 檢查是否有其他日誌配置覆蓋

### **問題**: 應用程式日誌也不顯示了

**解決方案**:
1. 檢查 `com.nanshan` logger 配置
2. 確認 appender 配置正確
3. 查看日誌文件是否正常生成

### **問題**: 日誌文件過大

**解決方案**:
1. 調整滾動策略
2. 減少日誌保留天數
3. 提高日誌級別

## 📞 **支援**

如果問題仍然存在，請提供：
- 完整的錯誤日誌
- `logback-spring.xml` 配置內容
- 應用程式啟動參數
- 環境信息（Java 版本、Spring Boot 版本等）

---

**修復完成！現在您的應用程式應該有清潔的日誌輸出了。** 🎉
