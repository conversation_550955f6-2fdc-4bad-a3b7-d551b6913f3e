package com.nanshan.demo.cache;

import com.nanshan.common.cache.annotation.EnableRedisSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

/**
 * Demo Common Cache 應用程式主類
 * 
 * 此應用程式展示如何使用 common-cache 套件的各種功能：
 * - Session 管理
 * - 快取管理
 * - 分散式鎖
 * - 自動清理
 * - 環境保護
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootApplication
@EnableRedisSupport(
    enableSessionManager = true,   // 啟用 Session 管理
    enableCacheManager = true,     // 啟用快取管理
    enableLockManager = true,      // 啟用鎖管理
    enableCleaner = true,          // 啟用清理器
    enableEnvGuard = true          // 啟用環境保護
)
public class DemoCommonCacheApplication {

    public static void main(String[] args) {
        log.info("=== Demo Common Cache Application Starting ===");

        ConfigurableApplicationContext context = SpringApplication.run(DemoCommonCacheApplication.class, args);

        // 動態獲取端口
        Environment env = context.getEnvironment();
        String port = env.getProperty("server.port", "8080");
        String baseUrl = "http://localhost:" + port;

        log.info("=== Demo Common Cache Application Started Successfully ===");
        log.info("Application is running on: {}", baseUrl);
        log.info("Available demo endpoints:");
        log.info("  - Session Demo: {}/demo/session", baseUrl);
        log.info("  - Cache Demo: {}/demo/cache", baseUrl);
        log.info("  - Lock Demo: {}/demo/lock", baseUrl);
        log.info("  - Cleaner Demo: {}/demo/cleaner", baseUrl);
        log.info("  - Comprehensive Demo: {}/demo/all", baseUrl);
        log.info("  - Config Check: {}/config/enabled-components", baseUrl);
        log.info("  - Health Check: {}/actuator/health", baseUrl);
    }
}
