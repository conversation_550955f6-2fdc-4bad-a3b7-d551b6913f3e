# Common Cache 配置示例
# 其他專案可以參考此配置來自定義 Redis 連線參數

# Spring Redis 基本配置（必須配置）
spring:
  data:
    redis:
      host: localhost                      # Redis 主機地址
      port: 6379                          # Redis 端口
      database: 0                         # Redis 資料庫索引
      username:                           # Redis 用戶名（Redis 6.0+ 支援，可選）
      password:                           # Redis 密碼（如果有的話）
      timeout: 2000ms                     # 連線超時時間
      # 如果使用 Redis Sentinel
      # sentinel:
      #   master: mymaster
      #   nodes:
      #     - localhost:26379
      #     - localhost:26380
      # 如果使用 Redis Cluster
      # cluster:
      #   nodes:
      #     - localhost:7000
      #     - localhost:7001

common:
  cache:
    # Redis 連線進階配置（可選，使用預設值即可）
    redis:
      # 連線池配置
      connection-pool:
        pool-size: 64                    # 連線池大小，預設 64
        minimum-idle-size: 10            # 最小空閒連線數，預設 10
        idle-connection-timeout: 10000   # 空閒連線超時時間（毫秒），預設 10000
      
      # 超時配置
      timeout:
        connect-timeout: 10000           # 連線超時時間（毫秒），預設 10000
        command-timeout: 3000            # 命令執行超時時間（毫秒），預設 3000
      
      # 重試配置
      retry:
        attempts: 3                      # 重試次數，預設 3
        interval: 1500                   # 重試間隔（毫秒），預設 1500
      
      # 執行緒池配置
      thread-pool:
        threads: 16                      # 執行緒數，預設 16
        netty-threads: 32                # Netty 執行緒數，預設 32
      
      # 其他配置
      misc:
        keep-alive: true                 # 是否啟用 Keep Alive，預設 true
        tcp-no-delay: true               # 是否啟用 TCP No Delay，預設 true
    
    # 環境保護設定
    env-guard:
      enabled: true                      # 是否啟用環境保護，預設 true
      production-profiles:               # 生產環境 Profile 清單
        - prod
        - production
      dangerous-operations-enabled: false # 是否允許危險操作，預設 false
    
    # Session 管理配置
    session:
      default-ttl: 1800                  # 預設 TTL（秒），預設 1800（30分鐘）
      key-prefix: "session"              # Key 前綴，預設 "session"
      cleanup-interval: 300              # 清理間隔（秒），預設 300（5分鐘）
      auto-renewal: true                 # 是否啟用自動續期，預設 true
      renewal-threshold: 0.2             # 續期閾值（剩餘 TTL 比例），預設 0.2（20%）
    
    # 快取管理配置
    cache:
      default-ttl: 3600                  # 預設 TTL（秒），預設 3600（1小時）
      key-prefix: "cache"                # Key 前綴，預設 "cache"
      auto-renewal: true                 # 是否啟用自動續期，預設 true
      renewal-threshold: 0.2             # 續期閾值（剩餘 TTL 比例），預設 0.2（20%）
      max-size: 10000                    # 最大快取大小，預設 10000
    
    # 鎖管理配置
    lock:
      default-lease-time: 30             # 預設租約時間（秒），預設 30
      default-wait-time: 10              # 預設等待時間（秒），預設 10
      key-prefix: "lock"                 # Key 前綴，預設 "lock"
      fair-lock-enabled: true            # 是否啟用公平鎖，預設 true
      watchdog-enabled: true             # 是否啟用看門狗機制，預設 true
    
    # 清理器配置
    cleaner:
      enabled: true                      # 是否啟用清理器，預設 true
      schedule-interval: 600             # 排程間隔（秒），預設 600（10分鐘）
      batch-size: 1000                   # 批次大小，預設 1000
      expired-key-scan-count: 100        # 過期 Key 掃描數量，預設 100
      statistics-enabled: true           # 是否啟用統計，預設 true