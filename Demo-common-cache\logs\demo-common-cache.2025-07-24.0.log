2025-07-24 10:36:03.213 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 10:36:03.231 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 10:36:03.232 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 10:36:03.232 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 10:36:03.232 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 10:36:03.237 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 10:36:03.237 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-24 10:41:03.250 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 10:41:03.250 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 10:41:03.250 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 10:41:03.250 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 10:41:03.250 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 10:41:03.250 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 10:41:03.250 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-24 10:46:03.267 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 10:46:03.270 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 10:46:03.270 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 10:46:03.270 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 10:46:03.270 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 10:46:03.273 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 10:46:03.273 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=5ms
2025-07-24 10:51:03.277 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 10:51:03.281 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 10:51:03.281 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 10:51:03.281 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 10:51:03.281 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 10:51:03.281 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 10:51:03.281 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-24 10:56:03.295 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 10:56:03.295 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 10:56:03.295 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 10:56:03.295 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 10:56:03.295 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 10:56:03.295 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 10:56:03.295 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-24 11:01:03.312 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:01:03.315 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:01:03.315 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:01:03.316 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:01:03.316 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:01:03.318 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:01:03.318 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-24 11:05:04.584 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-24 11:06:03.329 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:06:03.329 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:06:03.329 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:06:03.334 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:06:03.334 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:06:03.335 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:06:03.335 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-24 11:11:03.343 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:11:03.347 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:11:03.347 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:11:03.350 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:11:03.350 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:11:03.350 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:11:03.351 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-24 11:16:03.355 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:16:03.359 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:16:03.359 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:16:03.363 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:16:03.363 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:16:03.366 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:16:03.366 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-24 11:21:03.371 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:21:03.375 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:21:03.375 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:21:03.375 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:21:03.375 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:21:03.380 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:21:03.380 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-24 11:26:03.381 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:26:03.387 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:26:03.387 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:26:03.391 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:26:03.391 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:26:03.394 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:26:03.394 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=11ms
2025-07-24 11:31:03.403 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:31:03.406 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:31:03.406 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:31:03.409 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:31:03.410 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:31:03.410 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:31:03.411 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-24 11:35:04.604 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-24 11:36:03.421 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:36:03.425 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:36:03.427 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:36:03.433 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:36:03.433 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:36:03.438 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:36:03.438 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=15ms
2025-07-24 11:41:03.444 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:41:03.447 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:41:03.447 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:41:03.450 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:41:03.450 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:41:03.454 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:41:03.454 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-24 11:46:03.466 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:46:03.470 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:46:03.470 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:46:03.471 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:46:03.472 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:46:03.474 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:46:03.474 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-24 11:51:03.475 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:51:03.480 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:51:03.480 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:51:03.481 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:51:03.482 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:51:03.483 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:51:03.483 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-24 11:56:03.492 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 11:56:03.495 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 11:56:03.495 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 11:56:03.495 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 11:56:03.495 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 11:56:03.498 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 11:56:03.498 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-24 12:01:03.507 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 12:01:03.511 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 12:01:03.511 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 12:01:03.511 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 12:01:03.511 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 12:01:03.517 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 12:01:03.517 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-24 12:05:04.621 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-24 12:06:03.525 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 12:06:03.528 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 12:06:03.529 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 12:06:03.531 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 12:06:03.532 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 12:06:03.534 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 12:06:03.534 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-24 12:11:03.547 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 12:11:03.551 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 12:11:03.551 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 12:11:03.555 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 12:11:03.555 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 12:11:03.555 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 12:11:03.555 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-24 13:31:54.327 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-24 14:39:03.267 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-24 14:40:21.594 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 14:40:21.598 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 14:40:21.598 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 14:40:21.599 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 14:40:21.599 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 14:40:21.601 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 14:40:21.601 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-24 14:45:21.602 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 14:45:21.607 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 14:45:21.607 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 14:45:21.609 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 14:45:21.611 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 14:45:21.614 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 14:45:21.614 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=11ms
2025-07-24 14:50:21.621 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 14:50:21.625 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 14:50:21.625 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 14:50:21.630 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 14:50:21.630 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 14:50:21.633 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 14:50:21.633 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=12ms
2025-07-24 14:55:21.636 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 14:55:21.640 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 14:55:21.640 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 14:55:21.644 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 14:55:21.644 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 14:55:21.646 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 14:55:21.646 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-24 15:00:21.659 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:00:21.664 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:00:21.664 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:00:21.668 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:00:21.668 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:00:21.670 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:00:21.671 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-24 15:05:21.680 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:05:21.685 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:05:21.685 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:05:21.690 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:05:21.691 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:05:21.693 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:05:21.693 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=12ms
2025-07-24 15:09:03.272 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-24 15:10:21.708 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:10:21.708 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:10:21.712 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:10:21.712 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:10:21.714 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:10:21.714 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:10:21.714 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-24 15:15:21.726 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:15:21.729 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:15:21.730 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:15:21.733 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:15:21.733 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:15:21.736 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:15:21.737 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-24 15:20:21.751 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:20:21.755 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:20:21.756 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:20:21.759 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:20:21.759 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:20:21.763 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:20:21.763 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=12ms
2025-07-24 15:25:21.764 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:25:21.770 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:25:21.770 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:25:21.772 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:25:21.772 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:25:21.775 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:25:21.775 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-24 15:30:21.793 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:30:21.799 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:30:21.799 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:30:21.800 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:30:21.800 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:30:21.805 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:30:21.805 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=12ms
2025-07-24 15:35:21.818 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:35:21.828 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:35:21.828 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:35:21.831 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:35:21.831 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:35:21.833 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:35:21.833 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-24 15:39:03.285 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-24 15:40:21.837 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:40:21.842 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:40:21.844 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:40:21.846 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:40:21.846 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:40:21.847 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:40:21.847 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-24 15:45:21.852 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:45:21.858 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:45:21.860 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:45:21.863 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:45:21.868 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:45:21.872 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:45:21.878 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=22ms
2025-07-24 15:50:21.899 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:50:21.901 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:50:21.901 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:50:21.906 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:50:21.908 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:50:21.910 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:50:21.910 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-24 15:55:21.912 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 15:55:21.918 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 15:55:21.919 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 15:55:21.922 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 15:55:21.922 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 15:55:21.924 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 15:55:21.925 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-24 16:00:21.927 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-24 16:00:21.930 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-24 16:00:21.932 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-24 16:00:21.934 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-24 16:00:21.935 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-24 16:00:21.936 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-24 16:00:21.937 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
