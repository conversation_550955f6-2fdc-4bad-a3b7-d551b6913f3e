package com.nanshan.common.cache.service;

import com.nanshan.common.cache.annotation.EnableRedisSupport;
import com.nanshan.common.cache.config.TestConfig;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Redis 生產環境安全保護測試
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(
    classes = RedisDataAccessorProductionSafetyTest.ProductionSafetyTestApplication.class,
    properties = {
        "spring.profiles.active=prod",
        "common.cache.env-guard.enabled=true",
        "common.cache.env-guard.production-profiles=prod,production",
        "common.cache.env-guard.dangerous-operations-enabled=false"
    }
)
@ActiveProfiles("prod")
public class RedisDataAccessorProductionSafetyTest {

    @SpringBootApplication
    @EnableRedisSupport
    @Import(TestConfig.class)
    public static class ProductionSafetyTestApplication {
        public static void main(String[] args) {
            SpringApplication.run(ProductionSafetyTestApplication.class, args);
        }
    }

    @Autowired
    private RedisDataAccessor redisDataAccessor;

    @BeforeEach
    void setUp() {
        // 清理測試環境並設置測試資料
        redisDataAccessor.setString("test:key1", "value1");
        redisDataAccessor.setString("test:key2", "value2");
        redisDataAccessor.setString("test:key3", "value3");
    }

    @AfterEach
    void tearDown() {
        // 清理測試資料
        redisDataAccessor.delete("test:key1", "test:key2", "test:key3");
    }

    /**
     * 測試在生產環境中 flushDatabase 操作被阻止
     */
    @Test
    void testFlushDatabaseBlockedInProdEnvironment() {
        // 驗證測試資料存在
        assertThat(redisDataAccessor.exists("test:key1")).isTrue();
        assertThat(redisDataAccessor.exists("test:key2")).isTrue();
        assertThat(redisDataAccessor.exists("test:key3")).isTrue();

        // 執行 flushDatabase（應該被阻止）
        boolean result = redisDataAccessor.flushDatabase();

        // 驗證操作被阻止
        assertThat(result).isFalse();

        // 驗證資料仍然存在（沒有被刪除）
        assertThat(redisDataAccessor.exists("test:key1")).isTrue();
        assertThat(redisDataAccessor.exists("test:key2")).isTrue();
        assertThat(redisDataAccessor.exists("test:key3")).isTrue();
    }

    /**
     * 測試在生產環境中 flushAllDatabases 操作被阻止
     */
    @Test
    void testFlushAllDatabasesBlockedInProdEnvironment() {
        // 驗證測試資料存在
        assertThat(redisDataAccessor.exists("test:key1")).isTrue();
        assertThat(redisDataAccessor.exists("test:key2")).isTrue();
        assertThat(redisDataAccessor.exists("test:key3")).isTrue();

        // 執行 flushAllDatabases（應該被阻止）
        boolean result = redisDataAccessor.flushAllDatabases();

        // 驗證操作被阻止
        assertThat(result).isFalse();

        // 驗證資料仍然存在（沒有被刪除）
        assertThat(redisDataAccessor.exists("test:key1")).isTrue();
        assertThat(redisDataAccessor.exists("test:key2")).isTrue();
        assertThat(redisDataAccessor.exists("test:key3")).isTrue();
    }

    /**
     * 測試在生產環境中 KEYS * 查詢被限制
     */
    @Test
    void testKeysOperationBlockedInProdEnvironment() {
        // 執行 keys 操作（應該被阻止）
        var result = redisDataAccessor.keys("test:*");

        // 驗證操作被阻止，返回空集合
        assertThat(result).isEmpty();
    }

    /**
     * 內部測試類 - 生產環境但允許危險操作
     */
    @SpringBootTest(
        classes = RedisDataAccessorProductionSafetyTest.ProductionSafetyTestApplication.class,
        properties = {
            "spring.profiles.active=prod",
            "common.cache.env-guard.enabled=true",
            "common.cache.env-guard.production-profiles=prod,production",
            "common.cache.env-guard.dangerous-operations-enabled=true"
        }
    )
    @ActiveProfiles("prod")
    static class ProdEnvironmentWithDangerousOperationsEnabledTest {

        @Autowired
        private RedisDataAccessor redisDataAccessor;

        @BeforeEach
        void setUp() {
            redisDataAccessor.setString("test:key1", "value1");
            redisDataAccessor.setString("test:key2", "value2");
        }

        @AfterEach
        void tearDown() {
            redisDataAccessor.delete("test:key1", "test:key2");
        }

        /**
         * 測試在生產環境中啟用危險操作時 flushDatabase 可以執行
         */
        @Test
        void testFlushDatabaseAllowedWhenDangerousOperationsEnabled() {
            // 驗證測試資料存在
            assertThat(redisDataAccessor.exists("test:key1")).isTrue();
            assertThat(redisDataAccessor.exists("test:key2")).isTrue();

            // 執行 flushDatabase（應該被允許）
            boolean result = redisDataAccessor.flushDatabase();

            // 驗證操作成功
            assertThat(result).isTrue();

            // 驗證所有鍵都被刪除
            assertThat(redisDataAccessor.exists("test:key1")).isFalse();
            assertThat(redisDataAccessor.exists("test:key2")).isFalse();
        }

        /**
         * 測試在生產環境中啟用危險操作時 flushAllDatabases 可以執行
         */
        @Test
        void testFlushAllDatabasesAllowedWhenDangerousOperationsEnabled() {
            // 驗證測試資料存在
            assertThat(redisDataAccessor.exists("test:key1")).isTrue();
            assertThat(redisDataAccessor.exists("test:key2")).isTrue();

            // 執行 flushAllDatabases（應該被允許）
            boolean result = redisDataAccessor.flushAllDatabases();

            // 驗證操作成功
            assertThat(result).isTrue();

            // 驗證所有鍵都被刪除
            assertThat(redisDataAccessor.exists("test:key1")).isFalse();
            assertThat(redisDataAccessor.exists("test:key2")).isFalse();
        }

        /**
         * 測試在生產環境中啟用危險操作時 KEYS * 查詢可以執行
         */
        @Test
        void testKeysOperationAllowedWhenDangerousOperationsEnabled() {
            // 執行 keys 操作（應該被允許）
            var result = redisDataAccessor.keys("test:*");

            // 驗證操作成功，返回匹配的鍵
            assertThat(result).isNotEmpty();
            assertThat(result).contains("test:key1", "test:key2");
        }
    }
}
