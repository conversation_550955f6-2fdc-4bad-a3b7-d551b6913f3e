<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定義日誌輸出格式 -->
    <property name="CONSOLE_LOG_PATTERN" 
              value="%d{HH:mm:ss.SSS} [%thread] %highlight(%-5level) %cyan(%logger{36}) - %msg%n"/>
    
    <property name="FILE_LOG_PATTERN" 
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"/>

    <!-- 控制台輸出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 文件輸出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/demo-common-cache.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/demo-common-cache.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 根日誌級別 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>

    <!-- 應用程式日誌 -->
    <logger name="com.nanshan" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- Redisson 日誌控制 - 關鍵配置 -->
    <logger name="org.redisson" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- Redisson 命令執行日誌 - 完全關閉 DEBUG -->
    <logger name="org.redisson.command" level="ERROR" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <!-- Redisson 連線日誌 -->
    <logger name="org.redisson.connection" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- Spring Data Redis 日誌 -->
    <logger name="org.springframework.data.redis" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- Lettuce 連線日誌 -->
    <logger name="io.lettuce.core" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- Netty 日誌 -->
    <logger name="io.netty" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- Spring Boot 日誌 -->
    <logger name="org.springframework.boot" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- Spring Web 日誌 -->
    <logger name="org.springframework.web" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- SpringDoc OpenAPI 日誌 -->
    <logger name="org.springdoc" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- 開發環境配置 -->
    <springProfile name="dev">
        <logger name="com.nanshan" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </logger>
    </springProfile>

    <!-- 測試環境配置 -->
    <springProfile name="test">
        <logger name="com.nanshan" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </logger>
        <logger name="org.redisson" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </logger>
    </springProfile>

    <!-- 生產環境配置 -->
    <springProfile name="prod">
        <logger name="com.nanshan" level="INFO" additivity="false">
            <appender-ref ref="FILE"/>
        </logger>
        <logger name="org.redisson" level="ERROR" additivity="false">
            <appender-ref ref="FILE"/>
        </logger>
        <root level="WARN">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>

</configuration>
