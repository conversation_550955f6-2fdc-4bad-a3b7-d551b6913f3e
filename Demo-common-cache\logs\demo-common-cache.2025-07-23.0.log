2025-07-23 00:02:13.408 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:02:13.412 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:02:13.413 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:02:13.413 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:02:13.414 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:02:13.423 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:02:13.423 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=13ms
2025-07-23 00:07:13.427 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:07:13.429 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:07:13.431 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:07:13.432 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:07:13.433 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:07:13.435 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:07:13.435 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 00:12:13.448 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:12:13.453 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:12:13.453 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:12:13.455 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:12:13.455 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:12:13.457 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:12:13.457 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 00:13:20.619 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 00:17:13.461 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:17:13.465 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:17:13.465 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:17:13.467 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:17:13.467 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:17:13.469 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:17:13.469 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 00:22:13.478 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:22:13.482 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:22:13.482 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:22:13.485 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:22:13.485 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:22:13.489 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:22:13.489 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-23 00:27:13.505 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:27:13.507 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:27:13.507 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:27:13.509 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:27:13.509 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:27:13.512 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:27:13.512 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-23 00:32:13.525 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:32:13.529 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:32:13.529 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:32:13.531 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:32:13.532 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:32:13.533 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:32:13.533 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 00:37:13.547 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:37:13.552 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:37:13.552 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:37:13.554 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:37:13.554 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:37:13.555 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:37:13.555 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 00:42:13.569 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:42:13.570 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:42:13.570 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:42:13.572 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:42:13.572 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:42:13.573 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:42:13.573 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-23 00:43:20.627 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 00:47:13.575 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:47:13.578 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:47:13.578 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:47:13.580 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:47:13.580 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:47:13.584 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:47:13.584 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-23 00:52:13.585 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:52:13.587 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:52:13.587 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:52:13.588 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:52:13.588 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:52:13.589 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:52:13.589 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 00:57:13.599 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 00:57:13.603 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 00:57:13.603 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 00:57:13.604 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 00:57:13.604 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 00:57:13.605 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 00:57:13.605 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=5ms
2025-07-23 01:02:13.611 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:02:13.614 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:02:13.614 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:02:13.616 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:02:13.616 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:02:13.619 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:02:13.619 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 01:07:13.628 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:07:13.633 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:07:13.633 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:07:13.635 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:07:13.635 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:07:13.638 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:07:13.638 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-23 01:12:13.640 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:12:13.644 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:12:13.644 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:12:13.645 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:12:13.645 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:12:13.647 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:12:13.647 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 01:13:20.641 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 01:17:13.649 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:17:13.655 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:17:13.655 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:17:13.656 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:17:13.656 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:17:13.662 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:17:13.662 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=13ms
2025-07-23 01:22:13.671 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:22:13.672 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:22:13.672 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:22:13.676 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:22:13.676 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:22:13.679 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:22:13.679 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 01:27:13.686 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:27:13.689 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:27:13.689 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:27:13.694 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:27:13.694 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:27:13.699 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:27:13.700 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=14ms
2025-07-23 01:32:13.702 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:32:13.706 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:32:13.709 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:32:13.711 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:32:13.711 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:32:13.712 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:32:13.712 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-23 01:37:13.724 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:37:13.730 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:37:13.730 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:37:13.733 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:37:13.733 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:37:13.735 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:37:13.735 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-23 01:42:13.742 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:42:13.746 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:42:13.746 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:42:13.748 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:42:13.748 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:42:13.750 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:42:13.750 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 01:43:20.652 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 01:47:13.759 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:47:13.763 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:47:13.764 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:47:13.766 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:47:13.766 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:47:13.768 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:47:13.768 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 01:52:13.773 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:52:13.777 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:52:13.777 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:52:13.781 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:52:13.781 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:52:13.783 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:52:13.783 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-23 01:57:13.791 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 01:57:13.797 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 01:57:13.797 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 01:57:13.799 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 01:57:13.799 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 01:57:13.802 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 01:57:13.802 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-23 02:02:13.818 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:02:13.825 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:02:13.825 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:02:13.826 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:02:13.826 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:02:13.828 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:02:13.829 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-23 02:07:13.831 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:07:13.835 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:07:13.835 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:07:13.838 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:07:13.838 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:07:13.841 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:07:13.841 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-23 02:12:13.849 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:12:13.851 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:12:13.852 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:12:13.853 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:12:13.853 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:12:13.855 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:12:13.855 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-23 02:13:20.666 [redisson-netty-2-11] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 02:17:13.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:17:13.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:17:13.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:17:13.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:17:13.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:17:13.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:17:13.857 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 02:22:13.863 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:22:13.865 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:22:13.865 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:22:13.866 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:22:13.866 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:22:13.866 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:22:13.866 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 02:27:13.871 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:27:13.871 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:27:13.871 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:27:13.871 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:27:13.871 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:27:13.871 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:27:13.871 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 02:32:13.874 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:32:13.876 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:32:13.876 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:32:13.876 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:32:13.876 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:32:13.877 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:32:13.877 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 02:37:13.889 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:37:13.890 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:37:13.890 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:37:13.890 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:37:13.890 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:37:13.890 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:37:13.890 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=1ms
2025-07-23 02:42:13.898 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:42:13.898 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:42:13.898 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:42:13.898 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:42:13.898 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:42:13.898 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:42:13.898 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 02:43:20.677 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 02:47:13.910 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:47:13.910 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:47:13.910 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:47:13.910 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:47:13.910 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:47:13.910 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:47:13.910 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 02:52:13.924 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:52:13.924 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:52:13.924 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:52:13.927 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:52:13.927 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:52:13.927 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:52:13.927 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 02:57:13.943 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 02:57:13.944 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 02:57:13.944 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 02:57:13.944 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 02:57:13.944 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 02:57:13.944 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 02:57:13.944 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=1ms
2025-07-23 03:02:13.957 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:02:13.957 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:02:13.957 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:02:13.957 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:02:13.957 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:02:13.957 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:02:13.957 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 03:07:13.966 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:07:13.968 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:07:13.968 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:07:13.968 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:07:13.968 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:07:13.968 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:07:13.968 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 03:12:13.978 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:12:13.978 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:12:13.978 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:12:13.978 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:12:13.978 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:12:13.978 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:12:13.978 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 03:13:20.692 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 03:17:13.988 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:17:13.988 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:17:13.988 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:17:13.988 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:17:13.988 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:17:13.991 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:17:13.991 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 03:22:14.002 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:22:14.004 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:22:14.004 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:22:14.004 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:22:14.004 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:22:14.005 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:22:14.005 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 03:27:14.015 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:27:14.016 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:27:14.016 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:27:14.017 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:27:14.017 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:27:14.017 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:27:14.017 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 03:32:14.025 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:32:14.025 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:32:14.025 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:32:14.025 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:32:14.025 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:32:14.025 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:32:14.025 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 03:37:14.034 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:37:14.037 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:37:14.037 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:37:14.037 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:37:14.037 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:37:14.038 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:37:14.038 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 03:42:14.050 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:42:14.052 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:42:14.052 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:42:14.052 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:42:14.052 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:42:14.052 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:42:14.052 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 03:43:20.710 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 03:47:14.062 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:47:14.062 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:47:14.062 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:47:14.062 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:47:14.062 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:47:14.062 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:47:14.062 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 03:52:14.078 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:52:14.078 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:52:14.078 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:52:14.078 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:52:14.078 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:52:14.078 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:52:14.078 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 03:57:14.083 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 03:57:14.083 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 03:57:14.083 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 03:57:14.083 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 03:57:14.083 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 03:57:14.083 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 03:57:14.083 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 04:02:14.089 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:02:14.089 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:02:14.089 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:02:14.089 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:02:14.089 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:02:14.089 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:02:14.089 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 04:07:14.094 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:07:14.094 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:07:14.094 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:07:14.094 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:07:14.094 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:07:14.094 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:07:14.094 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 04:12:14.104 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:12:14.106 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:12:14.106 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:12:14.107 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:12:14.107 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:12:14.107 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:12:14.107 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 04:13:20.715 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 04:17:14.124 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:17:14.125 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:17:14.125 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:17:14.125 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:17:14.125 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:17:14.125 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:17:14.125 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=1ms
2025-07-23 04:22:14.141 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:22:14.141 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:22:14.141 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:22:14.141 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:22:14.141 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:22:14.144 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:22:14.144 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 04:27:14.162 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:27:14.162 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:27:14.162 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:27:14.165 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:27:14.165 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:27:14.165 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:27:14.165 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 04:32:14.169 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:32:14.170 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:32:14.170 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:32:14.170 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:32:14.170 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:32:14.171 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:32:14.171 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 04:37:14.183 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:37:14.183 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:37:14.183 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:37:14.184 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:37:14.184 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:37:14.184 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:37:14.184 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=1ms
2025-07-23 04:42:14.197 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:42:14.198 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:42:14.198 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:42:14.198 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:42:14.198 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:42:14.199 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:42:14.199 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 04:43:20.723 [redisson-netty-2-11] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 04:47:14.210 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:47:14.210 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:47:14.210 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:47:14.210 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:47:14.210 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:47:14.210 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:47:14.210 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 04:52:16.317 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 04:52:16.318 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 04:52:16.318 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 04:52:16.319 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 04:52:16.319 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 04:52:16.319 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 04:52:16.319 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 06:50:27.248 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 06:50:27.249 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 06:50:27.249 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 06:50:27.251 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 06:50:27.251 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 06:50:27.251 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 06:50:27.251 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 06:55:27.258 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 06:55:27.259 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 06:55:27.260 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 06:55:27.260 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 06:55:27.260 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 06:55:27.261 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 06:55:27.261 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 07:00:27.272 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 07:00:27.273 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 07:00:27.273 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 07:00:27.273 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 07:00:27.273 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 07:00:27.275 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 07:00:27.275 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 10:36:02.313 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 10:36:02.316 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 10:36:02.316 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 10:36:02.318 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 10:36:02.318 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 10:36:02.320 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 10:36:02.320 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 10:37:05.664 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 10:41:02.324 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 10:41:02.328 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 10:41:02.328 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 10:41:02.329 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 10:41:02.329 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 10:41:02.330 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 10:41:02.330 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-23 10:46:02.343 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 10:46:02.347 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 10:46:02.347 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 10:46:02.351 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 10:46:02.351 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 10:46:02.355 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 10:46:02.355 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=11ms
2025-07-23 10:51:02.369 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 10:51:02.374 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 10:51:02.374 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 10:51:02.377 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 10:51:02.377 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 10:51:02.380 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 10:51:02.380 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-23 10:56:02.391 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 10:56:02.395 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 10:56:02.395 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 10:56:02.398 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 10:56:02.398 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 10:56:02.401 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 10:56:02.401 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-23 11:01:02.416 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:01:02.420 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:01:02.420 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:01:02.422 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:01:02.422 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:01:02.424 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:01:02.424 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 11:06:02.436 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:06:02.438 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:06:02.438 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:06:02.439 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:06:02.440 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:06:02.441 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:06:02.441 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-23 11:07:05.674 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 11:11:02.456 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:11:02.458 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:11:02.458 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:11:02.461 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:11:02.461 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:11:02.464 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:11:02.464 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 11:16:02.466 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:16:02.468 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:16:02.468 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:16:02.468 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:16:02.468 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:16:02.468 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:16:02.474 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 11:21:02.486 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:21:02.488 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:21:02.489 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:21:02.489 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:21:02.491 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:21:02.491 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:21:02.491 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=5ms
2025-07-23 11:26:02.507 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:26:02.510 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:26:02.510 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:26:02.512 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:26:02.512 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:26:02.514 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:26:02.514 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-23 11:31:02.528 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:31:02.532 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:31:02.532 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:31:02.534 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:31:02.534 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:31:02.537 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:31:02.537 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 11:36:02.550 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:36:02.553 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:36:02.554 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:36:02.556 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:36:02.556 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:36:02.557 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:36:02.558 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 11:37:05.687 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 11:41:02.560 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:41:02.563 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:41:02.563 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:41:02.565 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:41:02.565 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:41:02.567 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:41:02.567 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 11:46:02.578 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:46:02.578 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:46:02.578 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:46:02.578 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:46:02.578 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:46:02.578 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:46:02.578 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 11:51:02.595 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:51:02.598 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:51:02.598 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:51:02.600 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:51:02.600 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:51:02.600 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:51:02.601 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-23 11:56:02.608 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 11:56:02.611 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 11:56:02.611 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 11:56:02.611 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 11:56:02.611 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 11:56:02.616 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 11:56:02.616 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 12:06:50.417 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:06:50.419 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:06:50.419 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:06:50.419 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:06:50.419 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:06:50.425 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:06:50.425 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 12:07:05.701 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 12:11:50.441 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:11:50.443 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:11:50.443 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:11:50.444 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:11:50.444 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:11:50.445 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:11:50.445 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-23 12:16:50.458 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:16:50.462 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:16:50.462 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:16:50.462 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:16:50.462 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:16:50.462 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:16:50.462 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-23 12:21:50.474 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:21:50.476 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:21:50.476 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:21:50.478 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:21:50.478 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:21:50.480 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:21:50.480 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-23 12:26:50.487 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:26:50.489 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:26:50.489 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:26:50.490 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:26:50.490 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:26:50.492 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:26:50.492 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=5ms
2025-07-23 12:31:50.505 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:31:50.507 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:31:50.507 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:31:50.508 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:31:50.508 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:31:50.508 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:31:50.508 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 12:36:50.525 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:36:50.528 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:36:50.528 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:36:50.528 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:36:50.528 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:36:50.533 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:36:50.533 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 12:37:05.716 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 12:41:50.544 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:41:50.547 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:41:50.547 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:41:50.549 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:41:50.549 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:41:50.553 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:41:50.553 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 12:46:50.554 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:46:50.554 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:46:50.554 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:46:50.554 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:46:50.554 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:46:50.554 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:46:50.562 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 12:51:50.575 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:51:50.577 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:51:50.577 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:51:50.579 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:51:50.579 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:51:50.582 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:51:50.582 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 12:56:50.590 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 12:56:50.590 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 12:56:50.590 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 12:56:50.595 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 12:56:50.595 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 12:56:50.599 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 12:56:50.599 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-23 13:01:50.608 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:01:50.611 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:01:50.611 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:01:50.614 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:01:50.614 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:01:50.617 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:01:50.617 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 13:06:50.632 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:06:50.635 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:06:50.635 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:06:50.636 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:06:50.636 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:06:50.638 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:06:50.639 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-23 13:07:05.719 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 13:11:50.642 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:11:50.645 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:11:50.645 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:11:50.645 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:11:50.645 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:11:50.650 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:11:50.650 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 13:16:50.662 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:16:50.662 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:16:50.662 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:16:50.662 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:16:50.662 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:16:50.662 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:16:50.662 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 13:21:50.679 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:21:50.681 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:21:50.681 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:21:50.682 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:21:50.682 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:21:50.683 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:21:50.683 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-23 13:26:50.692 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:26:50.692 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:26:50.692 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:26:50.692 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:26:50.692 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:26:50.699 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:26:50.699 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=7ms
2025-07-23 13:31:50.714 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:31:50.716 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:31:50.716 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:31:50.719 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:31:50.719 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:31:50.719 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:31:50.719 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=5ms
2025-07-23 13:36:50.728 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:36:50.729 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:36:50.729 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:36:50.729 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:36:50.729 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:36:50.737 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:36:50.737 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-23 13:37:05.737 [redisson-netty-2-11] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 13:41:50.747 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:41:50.747 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:41:50.747 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:41:50.747 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:41:50.747 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:41:50.755 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:41:50.755 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 13:46:50.761 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:46:50.761 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:46:50.761 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:46:50.761 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:46:50.761 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:46:50.761 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:46:50.761 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 13:51:50.772 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:51:50.772 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:51:50.772 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:51:50.778 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:51:50.778 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:51:50.780 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:51:50.780 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 13:56:50.797 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 13:56:50.799 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 13:56:50.799 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 13:56:50.802 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 13:56:50.802 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 13:56:50.802 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 13:56:50.802 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 14:01:50.814 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:01:50.818 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:01:50.818 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:01:50.820 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:01:50.820 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:01:50.822 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:01:50.822 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=6ms
2025-07-23 14:06:50.832 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:06:50.832 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:06:50.832 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:06:50.832 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:06:50.832 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:06:50.840 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:06:50.840 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 14:07:05.751 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:10:26.389 [main] INFO  c.n.demo.config.RedisConfigurationValidationTest - Starting RedisConfigurationValidationTest using Java 21.0.7 with PID 32968 (started by Nofi in C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache)
2025-07-23 14:10:26.393 [main] DEBUG c.n.demo.config.RedisConfigurationValidationTest - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-07-23 14:10:26.393 [main] INFO  c.n.demo.config.RedisConfigurationValidationTest - The following 1 profile is active: "dev"
2025-07-23 14:10:30.028 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 14:10:30.028 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 14:10:30.052 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-07-23 14:10:30.970 [main] INFO  com.nanshan.common.cache.config.RedissonConfig - Configured Redisson single server: redis://localhost:6379
2025-07-23 14:10:31.134 [main] DEBUG com.nanshan.common.cache.config.RedissonConfig - Configured Redisson thread pools - threads: 8, netty threads: 16
2025-07-23 14:10:31.134 [main] INFO  com.nanshan.common.cache.config.RedissonConfig - Redisson client configured successfully
2025-07-23 14:10:31.276 [main] INFO  org.redisson.Version - Redisson 3.24.3
2025-07-23 14:10:31.669 [redisson-netty-2-3] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:10:31.691 [redisson-netty-2-4] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:10:31.807 [redisson-netty-2-7] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:10:31.823 [redisson-netty-2-9] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:10:31.845 [redisson-netty-2-11] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:10:31.856 [redisson-netty-2-13] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:10:31.950 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisDataAccessor
2025-07-23 14:10:31.958 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring CacheManager
2025-07-23 14:10:31.995 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring SessionManager
2025-07-23 14:10:32.355 [main] INFO  c.n.common.cache.service.impl.SessionManagerImpl - SessionManager Redisson optimizations initialized successfully
2025-07-23 14:10:32.365 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisCleaner
2025-07-23 14:10:32.562 [main] INFO  com.nanshan.demo.cache.service.AuthService - 初始化了 3 個示範用戶
2025-07-23 14:10:32.570 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring LockManager
2025-07-23 14:10:32.690 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisConnectionManager
2025-07-23 14:10:32.695 [redisson-netty-2-2] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:10:32.700 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection test successful
2025-07-23 14:10:32.702 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection manager initialized successfully
2025-07-23 14:10:33.282 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-23 14:10:36.347 [main] DEBUG i.l.core.resource.AddressResolverGroupProvider - Starting with netty's non-blocking DNS resolver library
2025-07-23 14:10:36.364 [main] DEBUG io.lettuce.core.resource.KqueueProvider - Starting without optional kqueue library
2025-07-23 14:10:36.391 [main] DEBUG io.lettuce.core.resource.IOUringProvider - Starting without optional io_uring library
2025-07-23 14:10:36.393 [main] DEBUG io.lettuce.core.resource.EpollProvider - Starting without optional epoll library
2025-07-23 14:10:36.396 [main] DEBUG io.lettuce.core.resource.DefaultClientResources - -Dio.netty.eventLoopThreads: 16
2025-07-23 14:10:36.478 [main] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Creating executor io.netty.util.concurrent.DefaultEventExecutorGroup
2025-07-23 14:10:36.557 [main] DEBUG io.lettuce.core.event.jfr.EventRecorderHolder - Starting with JFR support
2025-07-23 14:10:37.037 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Starting RedisMessageListenerContainer...
2025-07-23 14:10:37.038 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Postpone listening for Redis messages until actual listeners are added
2025-07-23 14:10:37.157 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:10:37.172 [main] INFO  c.n.demo.config.RedisConfigurationValidationTest - Started RedisConfigurationValidationTest in 11.434 seconds (process running for 14.252)
2025-07-23 14:10:37.407 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:10:37.549 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:10:37.549 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:10:37.553 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:10:37.553 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:10:37.556 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:10:37.574 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=148ms
2025-07-23 14:10:39.215 [SpringApplicationShutdownHook] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Stopped RedisMessageListenerContainer
2025-07-23 14:10:39.223 [SpringApplicationShutdownHook] DEBUG io.lettuce.core.resource.DefaultClientResources - Initiate shutdown (0, 2, SECONDS)
2025-07-23 14:10:39.225 [Thread-2] INFO  c.n.common.cache.service.RedisConnectionManager - RedissonClient shutdown completed
2025-07-23 14:10:39.226 [SpringApplicationShutdownHook] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Initiate shutdown (0, 2, SECONDS)
2025-07-23 14:10:39.248 [SpringApplicationShutdownHook] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection manager destroyed successfully
2025-07-23 14:11:50.850 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:11:50.850 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:11:50.850 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:11:50.850 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:11:50.850 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:11:50.858 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:11:50.858 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 14:12:38.127 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - Starting DemoCommonCacheApplication using Java 21.0.7 with PID 3148 (C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache\target\classes started by Nofi in C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache)
2025-07-23 14:12:38.127 [main] DEBUG com.nanshan.demo.cache.DemoCommonCacheApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-07-23 14:12:38.127 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - The following 1 profile is active: "dev"
2025-07-23 14:12:39.128 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 14:12:39.128 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 14:12:39.148 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-07-23 14:12:39.626 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-23 14:12:39.633 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-23 14:12:39.633 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 14:12:39.633 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-07-23 14:12:39.691 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-23 14:12:39.707 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1512 ms
2025-07-23 14:12:39.888 [main] INFO  com.nanshan.common.cache.config.RedissonConfig - Configured Redisson single server: redis://localhost:6379
2025-07-23 14:12:39.981 [main] DEBUG com.nanshan.common.cache.config.RedissonConfig - Configured Redisson thread pools - threads: 8, netty threads: 16
2025-07-23 14:12:39.985 [main] INFO  com.nanshan.common.cache.config.RedissonConfig - Redisson client configured successfully
2025-07-23 14:12:40.058 [main] INFO  org.redisson.Version - Redisson 3.24.3
2025-07-23 14:12:40.218 [redisson-netty-2-3] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:12:40.230 [redisson-netty-2-4] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:12:40.257 [redisson-netty-2-7] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:12:40.260 [redisson-netty-2-9] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:12:40.270 [redisson-netty-2-11] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:12:40.285 [redisson-netty-2-13] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:12:40.325 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisDataAccessor
2025-07-23 14:12:40.330 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring CacheManager
2025-07-23 14:12:40.334 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring SessionManager
2025-07-23 14:12:40.374 [main] INFO  c.n.common.cache.service.impl.SessionManagerImpl - SessionManager Redisson optimizations initialized successfully
2025-07-23 14:12:40.375 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisCleaner
2025-07-23 14:12:40.501 [main] INFO  com.nanshan.demo.cache.service.AuthService - 初始化了 3 個示範用戶
2025-07-23 14:12:40.501 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring LockManager
2025-07-23 14:12:40.518 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisConnectionManager
2025-07-23 14:12:40.526 [redisson-netty-2-2] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:12:40.530 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection test successful
2025-07-23 14:12:40.530 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection manager initialized successfully
2025-07-23 14:12:40.758 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-23 14:12:41.214 [main] DEBUG i.l.core.resource.AddressResolverGroupProvider - Starting with netty's non-blocking DNS resolver library
2025-07-23 14:12:41.214 [main] DEBUG io.lettuce.core.resource.KqueueProvider - Starting without optional kqueue library
2025-07-23 14:12:41.230 [main] DEBUG io.lettuce.core.resource.IOUringProvider - Starting without optional io_uring library
2025-07-23 14:12:41.230 [main] DEBUG io.lettuce.core.resource.EpollProvider - Starting without optional epoll library
2025-07-23 14:12:41.232 [main] DEBUG io.lettuce.core.resource.DefaultClientResources - -Dio.netty.eventLoopThreads: 16
2025-07-23 14:12:41.232 [main] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Creating executor io.netty.util.concurrent.DefaultEventExecutorGroup
2025-07-23 14:12:41.247 [main] DEBUG io.lettuce.core.event.jfr.EventRecorderHolder - Starting with JFR support
2025-07-23 14:12:41.390 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Starting RedisMessageListenerContainer...
2025-07-23 14:12:41.390 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Postpone listening for Redis messages until actual listeners are added
2025-07-23 14:12:41.437 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-23 14:12:41.437 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-23 14:12:41.437 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Stopped RedisMessageListenerContainer
2025-07-23 14:12:41.437 [main] DEBUG io.lettuce.core.resource.DefaultClientResources - Initiate shutdown (0, 2, SECONDS)
2025-07-23 14:12:41.437 [main] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Initiate shutdown (0, 2, SECONDS)
2025-07-23 14:12:41.474 [main] INFO  c.n.common.cache.service.RedisConnectionManager - RedissonClient shutdown completed
2025-07-23 14:12:41.474 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection manager destroyed successfully
2025-07-23 14:12:41.491 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-23 14:12:41.500 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-23 14:13:39.590 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - Starting DemoCommonCacheApplication using Java 21.0.7 with PID 24472 (C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache\target\classes started by Nofi in C:\Users\<USER>\Desktop\workspace\common-base\Demo-common-cache)
2025-07-23 14:13:39.590 [main] DEBUG com.nanshan.demo.cache.DemoCommonCacheApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-07-23 14:13:39.590 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - The following 1 profile is active: "dev"
2025-07-23 14:13:40.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 14:13:40.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 14:13:40.909 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-07-23 14:13:41.438 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-23 14:13:41.447 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-23 14:13:41.452 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 14:13:41.452 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-07-23 14:13:41.511 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-23 14:13:41.511 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1805 ms
2025-07-23 14:13:41.687 [main] INFO  com.nanshan.common.cache.config.RedissonConfig - Configured Redisson single server: redis://localhost:6379
2025-07-23 14:13:41.781 [main] DEBUG com.nanshan.common.cache.config.RedissonConfig - Configured Redisson thread pools - threads: 8, netty threads: 16
2025-07-23 14:13:41.783 [main] INFO  com.nanshan.common.cache.config.RedissonConfig - Redisson client configured successfully
2025-07-23 14:13:41.872 [main] INFO  org.redisson.Version - Redisson 3.24.3
2025-07-23 14:13:42.062 [redisson-netty-2-3] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:13:42.078 [redisson-netty-2-4] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:13:42.102 [redisson-netty-2-7] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:13:42.110 [redisson-netty-2-9] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:13:42.125 [redisson-netty-2-11] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:13:42.130 [redisson-netty-2-13] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:13:42.174 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisDataAccessor
2025-07-23 14:13:42.176 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring CacheManager
2025-07-23 14:13:42.176 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring SessionManager
2025-07-23 14:13:42.212 [main] INFO  c.n.common.cache.service.impl.SessionManagerImpl - SessionManager Redisson optimizations initialized successfully
2025-07-23 14:13:42.212 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisCleaner
2025-07-23 14:13:42.351 [main] INFO  com.nanshan.demo.cache.service.AuthService - 初始化了 3 個示範用戶
2025-07-23 14:13:42.353 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring LockManager
2025-07-23 14:13:42.376 [main] INFO  c.n.c.cache.config.CommonCacheAutoConfiguration - Configuring RedisConnectionManager
2025-07-23 14:13:42.379 [redisson-netty-2-2] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-23 14:13:42.390 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection test successful
2025-07-23 14:13:42.393 [main] INFO  c.n.common.cache.service.RedisConnectionManager - Redis connection manager initialized successfully
2025-07-23 14:13:42.637 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-23 14:13:43.153 [main] DEBUG i.l.core.resource.AddressResolverGroupProvider - Starting with netty's non-blocking DNS resolver library
2025-07-23 14:13:43.158 [main] DEBUG io.lettuce.core.resource.KqueueProvider - Starting without optional kqueue library
2025-07-23 14:13:43.158 [main] DEBUG io.lettuce.core.resource.IOUringProvider - Starting without optional io_uring library
2025-07-23 14:13:43.158 [main] DEBUG io.lettuce.core.resource.EpollProvider - Starting without optional epoll library
2025-07-23 14:13:43.158 [main] DEBUG io.lettuce.core.resource.DefaultClientResources - -Dio.netty.eventLoopThreads: 16
2025-07-23 14:13:43.166 [main] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Creating executor io.netty.util.concurrent.DefaultEventExecutorGroup
2025-07-23 14:13:43.175 [main] DEBUG io.lettuce.core.event.jfr.EventRecorderHolder - Starting with JFR support
2025-07-23 14:13:43.283 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Starting RedisMessageListenerContainer...
2025-07-23 14:13:43.283 [main] DEBUG o.s.d.redis.listener.RedisMessageListenerContainer - Postpone listening for Redis messages until actual listeners are added
2025-07-23 14:13:43.324 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-23 14:13:43.332 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-23 14:13:43.351 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - Started DemoCommonCacheApplication in 4.781 seconds (process running for 5.337)
2025-07-23 14:13:43.358 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:13:43.376 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:13:43.377 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:13:43.377 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:13:43.377 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:13:43.382 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:13:43.383 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=21ms
2025-07-23 14:13:43.484 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - === Demo Common Cache Application Started Successfully ===
2025-07-23 14:13:43.484 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - Application is running on: http://localhost:8080
2025-07-23 14:13:43.484 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication - Available demo endpoints:
2025-07-23 14:13:43.484 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Session Demo: http://localhost:8080/demo/session
2025-07-23 14:13:43.484 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Cache Demo: http://localhost:8080/demo/cache
2025-07-23 14:13:43.484 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Lock Demo: http://localhost:8080/demo/lock
2025-07-23 14:13:43.484 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Cleaner Demo: http://localhost:8080/demo/cleaner
2025-07-23 14:13:43.484 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Comprehensive Demo: http://localhost:8080/demo/all
2025-07-23 14:13:43.484 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Config Check: http://localhost:8080/config/enabled-components
2025-07-23 14:13:43.484 [main] INFO  com.nanshan.demo.cache.DemoCommonCacheApplication -   - Health Check: http://localhost:8080/actuator/health
2025-07-23 14:13:47.207 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:13:52.215 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - -1 elements evicted. Object name: sessions
2025-07-23 14:13:57.219 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:14:02.236 [redisson-netty-2-11] DEBUG org.redisson.eviction.MapCacheEvictionTask - -1 elements evicted. Object name: sessions
2025-07-23 14:14:07.251 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:14:14.266 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:14:24.284 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:14:39.287 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:15:01.303 [redisson-netty-2-11] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:15:34.315 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:16:23.330 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:17:36.335 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:18:43.389 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:18:43.391 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:18:43.391 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:18:43.391 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:18:43.391 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:18:43.391 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:18:43.391 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 14:19:25.357 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:22:08.376 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:23:43.413 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:23:43.413 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:23:43.413 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:23:43.413 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:23:43.413 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:23:43.423 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:23:43.423 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-23 14:26:12.387 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:28:43.428 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:28:43.428 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:28:43.428 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:28:43.442 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:28:43.446 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:28:43.451 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:28:43.451 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=23ms
2025-07-23 14:32:18.402 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:33:43.468 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:33:43.474 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:33:43.474 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:33:43.478 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:33:43.481 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:33:43.485 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:33:43.485 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=15ms
2025-07-23 14:38:43.493 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:38:43.493 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:38:43.493 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:38:43.500 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:38:43.500 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:38:43.502 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:38:43.504 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=11ms
2025-07-23 14:41:27.416 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:43:43.507 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:43:43.511 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:43:43.511 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:43:43.517 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:43:43.519 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:43:43.520 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:43:43.520 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=11ms
2025-07-23 14:48:43.540 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:48:43.546 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:48:43.546 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:48:43.549 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:48:43.551 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:48:43.557 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:48:43.558 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=15ms
2025-07-23 14:53:43.566 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:53:43.566 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:53:43.566 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:53:43.577 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:53:43.579 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:53:43.581 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:53:43.582 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=16ms
2025-07-23 14:55:10.425 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 14:58:43.593 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 14:58:43.597 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 14:58:43.598 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 14:58:43.600 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 14:58:43.601 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 14:58:43.603 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 14:58:43.603 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 15:03:43.619 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:03:43.626 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:03:43.626 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:03:43.628 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:03:43.629 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:03:43.633 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:03:43.633 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=11ms
2025-07-23 15:08:43.638 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:08:43.645 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:08:43.645 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:08:43.652 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:08:43.652 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:08:43.654 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:08:43.657 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=16ms
2025-07-23 15:13:43.674 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:13:43.678 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:13:43.681 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:13:43.683 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:13:43.689 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:13:43.693 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:13:43.694 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=19ms
2025-07-23 15:15:44.440 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 15:18:43.696 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:18:43.702 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:18:43.707 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:18:43.710 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:18:43.711 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:18:43.715 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:18:43.716 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=18ms
2025-07-23 15:23:43.720 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:23:43.727 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:23:43.727 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:23:43.733 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:23:43.733 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:23:43.736 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:23:43.736 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=16ms
2025-07-23 15:28:43.752 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:28:43.756 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:28:43.756 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:28:43.760 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:28:43.760 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:28:43.762 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:28:43.762 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-23 15:33:43.766 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:33:43.771 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:33:43.771 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:33:43.774 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:33:43.774 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:33:43.777 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:33:43.779 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=13ms
2025-07-23 15:38:43.795 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:38:43.802 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:38:43.803 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:38:43.806 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:38:43.808 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:38:43.812 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:38:43.816 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=17ms
2025-07-23 15:43:43.827 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:43:43.832 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:43:43.833 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:43:43.836 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:43:43.836 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:43:43.841 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:43:43.842 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=12ms
2025-07-23 15:45:44.460 [redisson-netty-2-11] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 15:48:43.853 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:48:43.857 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:48:43.859 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:48:43.861 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:48:43.862 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:48:43.866 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:48:43.868 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=13ms
2025-07-23 15:53:43.884 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:53:43.891 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:53:43.891 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:53:43.895 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:53:43.895 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:53:43.899 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:53:43.900 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=16ms
2025-07-23 15:58:43.904 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 15:58:43.910 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 15:58:43.910 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 15:58:43.912 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 15:58:43.914 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 15:58:43.916 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 15:58:43.916 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=9ms
2025-07-23 16:03:43.920 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:03:43.924 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:03:43.924 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:03:43.933 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:03:43.933 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:03:43.937 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:03:43.938 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=16ms
2025-07-23 16:08:43.941 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:08:43.947 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:08:43.947 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:08:43.949 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:08:43.949 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:08:43.953 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:08:43.954 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=13ms
2025-07-23 16:13:43.966 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:13:43.968 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:13:43.968 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:13:43.977 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:13:43.978 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:13:43.981 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:13:43.981 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=13ms
2025-07-23 16:15:44.479 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 16:18:43.983 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:18:43.988 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:18:43.989 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:18:43.992 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:18:43.994 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:18:43.996 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:18:43.997 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=11ms
2025-07-23 16:23:44.004 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:23:44.011 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:23:44.013 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:23:44.015 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:23:44.019 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:23:44.021 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:23:44.027 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=17ms
2025-07-23 16:28:44.039 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:28:44.041 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:28:44.041 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:28:44.048 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:28:44.048 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:28:44.051 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:28:44.051 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=10ms
2025-07-23 16:33:44.061 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:33:44.065 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:33:44.065 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:33:44.068 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:33:44.068 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:33:44.069 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:33:44.069 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=8ms
2025-07-23 16:39:29.127 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:39:29.129 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:39:29.129 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:39:29.129 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:39:29.129 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:39:29.131 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:39:29.131 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-23 16:44:29.143 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:44:29.144 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:44:29.145 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:44:29.145 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:44:29.145 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:44:29.146 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:44:29.146 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 16:45:40.408 [redisson-netty-2-7] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 16:49:29.150 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:49:29.152 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:49:29.152 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:49:29.153 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:49:29.153 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:49:29.153 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:49:29.153 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 16:54:29.164 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:54:29.164 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:54:29.164 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:54:29.167 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:54:29.167 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:54:29.167 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:54:29.167 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 16:59:29.173 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 16:59:29.173 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 16:59:29.173 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 16:59:29.175 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 16:59:29.175 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 16:59:29.175 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 16:59:29.175 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 17:04:29.181 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:04:29.182 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:04:29.182 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:04:29.183 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:04:29.183 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:04:29.183 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:04:29.183 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 17:09:29.194 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:09:29.194 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:09:29.196 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:09:29.196 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:09:29.196 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:09:29.198 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:09:29.198 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-23 17:14:29.212 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:14:29.212 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:14:29.212 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:14:29.212 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:14:29.212 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:14:29.212 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:14:29.212 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 17:15:40.417 [redisson-netty-2-3] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 17:19:29.229 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:19:29.230 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:19:29.231 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:19:29.231 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:19:29.231 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:19:29.232 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:19:29.232 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 17:24:29.237 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:24:29.237 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:24:29.237 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:24:29.237 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:24:29.237 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:24:29.237 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:24:29.237 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=0ms
2025-07-23 17:29:29.256 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:29:29.256 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:29:29.256 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:29:29.256 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:29:29.256 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:29:29.259 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:29:29.259 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 17:34:29.270 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:34:29.272 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:34:29.272 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:34:29.273 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:34:29.273 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:34:29.273 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:34:29.274 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-23 17:39:29.288 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:39:29.290 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:39:29.290 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:39:29.290 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:39:29.290 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:39:29.291 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:39:29.291 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 17:44:29.296 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:44:29.298 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:44:29.298 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:44:29.298 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:44:29.299 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:44:29.299 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:44:29.299 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=2ms
2025-07-23 17:45:40.421 [redisson-netty-2-13] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
2025-07-23 17:45:44.168 [http-nio-8080-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 17:45:44.168 [http-nio-8080-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-23 17:45:44.171 [http-nio-8080-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-23 17:45:44.701 [http-nio-8080-exec-4] INFO  org.springdoc.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 218 ms
2025-07-23 17:45:48.387 [http-nio-8080-exec-2] INFO  c.n.demo.cache.controller.CacheDemoController - 開始執行快取管理完整示範
2025-07-23 17:45:48.387 [http-nio-8080-exec-2] INFO  c.n.demo.cache.controller.CacheDemoController - 步驟 1: 基本快取操作示範
2025-07-23 17:45:48.408 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=PROD-001, ttl=3600s
2025-07-23 17:45:48.408 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.CacheDemoService - 儲存產品快取: PROD-001
2025-07-23 17:45:48.418 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=PROD-001
2025-07-23 17:45:48.419 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache renewed: type=product, id=PROD-001, ttl=7200s
2025-07-23 17:45:48.419 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.CacheDemoService - 快取基本操作示範完成
2025-07-23 17:45:48.419 [http-nio-8080-exec-2] INFO  c.n.demo.cache.controller.CacheDemoController - 步驟 2: 批量快取操作示範
2025-07-23 17:45:48.420 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=BATCH-PROD-005, ttl=3600s
2025-07-23 17:45:48.421 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=BATCH-PROD-003, ttl=3600s
2025-07-23 17:45:48.422 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=BATCH-PROD-004, ttl=3600s
2025-07-23 17:45:48.422 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=BATCH-PROD-001, ttl=3600s
2025-07-23 17:45:48.424 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=BATCH-PROD-002, ttl=3600s
2025-07-23 17:45:48.424 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.CacheDemoService - 批量儲存產品快取: 5 個產品
2025-07-23 17:45:48.425 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=BATCH-PROD-005
2025-07-23 17:45:48.427 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=BATCH-PROD-003
2025-07-23 17:45:48.428 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=BATCH-PROD-004
2025-07-23 17:45:48.430 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=BATCH-PROD-001
2025-07-23 17:45:48.432 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=BATCH-PROD-002
2025-07-23 17:45:48.436 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache removed: type=product, id=BATCH-PROD-005
2025-07-23 17:45:48.436 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache removed: type=product, id=BATCH-PROD-003
2025-07-23 17:45:48.436 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.CacheDemoService - 快取批量操作示範完成
2025-07-23 17:45:48.437 [http-nio-8080-exec-2] INFO  c.n.demo.cache.controller.CacheDemoController - 步驟 3: 快取計算操作示範
2025-07-23 17:45:48.437 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache miss: type=product, id=COMPUTE-PROD-001
2025-07-23 17:45:48.437 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.CacheDemoService - 快取未命中，執行產品資料計算 (模擬資料庫查詢): COMPUTE-PROD-001
2025-07-23 17:45:49.451 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=COMPUTE-PROD-001, ttl=3600s
2025-07-23 17:45:49.453 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=COMPUTE-PROD-001
2025-07-23 17:45:49.453 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.CacheDemoService - 快取計算操作示範完成，第一次耗時: 1014ms，第二次耗時: 2ms
2025-07-23 17:45:49.453 [http-nio-8080-exec-2] INFO  c.n.demo.cache.controller.CacheDemoController - 步驟 4: 快取統計資訊示範
2025-07-23 17:45:49.455 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=STATS-PROD-001, ttl=3600s
2025-07-23 17:45:49.457 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=STATS-PROD-002, ttl=3600s
2025-07-23 17:45:49.458 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache stored: type=product, id=STATS-PROD-003, ttl=3600s
2025-07-23 17:45:49.460 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=STATS-PROD-001
2025-07-23 17:45:49.461 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache hit: type=product, id=STATS-PROD-002
2025-07-23 17:45:49.461 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cache miss: type=product, id=NON-EXISTENT
2025-07-23 17:45:49.468 [http-nio-8080-exec-2] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:45:49.468 [http-nio-8080-exec-2] INFO  com.nanshan.demo.cache.service.CacheDemoService - 快取統計資訊示範完成
2025-07-23 17:45:49.469 [http-nio-8080-exec-2] INFO  c.n.demo.cache.controller.CacheDemoController - 快取管理完整示範執行完成，成功: true
2025-07-23 17:49:29.300 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:49:29.301 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:49:29.302 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:49:29.304 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:49:29.304 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:49:29.305 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:49:29.305 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-23 17:54:29.312 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:54:29.313 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:54:29.313 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:54:29.317 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:54:29.317 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:54:29.317 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:54:29.317 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=4ms
2025-07-23 17:59:29.321 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-23 17:59:29.321 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-23 17:59:29.321 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-23 17:59:29.324 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-23 17:59:29.324 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-23 17:59:29.324 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-23 17:59:29.324 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=3ms
2025-07-23 21:21:41.223 [redisson-netty-2-9] DEBUG org.redisson.eviction.MapCacheEvictionTask - 0 elements evicted. Object name: sessions
