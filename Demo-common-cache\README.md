# Demo Common Cache

[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2+-green.svg)](https://spring.io/projects/spring-boot)
[![Java Version](https://img.shields.io/badge/Java-17+-blue.svg)](https://openjdk.java.net/)
[![Redis](https://img.shields.io/badge/Redis-6.0+-red.svg)](https://redis.io/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![API Docs](https://img.shields.io/badge/API-Documented-orange.svg)](#api-端點文檔)

> 🎯 **Common Cache 功能展示應用** - 完整的 REST API 示範，展示企業級快取解決方案的所有功能

## 📋 **目錄**

- [Demo 應用介紹](#demo-應用介紹)
- [快速開始](#快速開始)
- [API 端點文檔](#api-端點文檔)
- [配置範例](#配置範例)
- [測試指南](#測試指南)
- [Docker 支援](#docker-支援)
- [故障排除](#故障排除)
- [效能測試](#效能測試)

## 🎯 **Demo 應用介紹**

Demo Common Cache 是一個完整的 Spring Boot 應用程式，專門用於展示 `common-cache` 框架的所有功能。它提供了豐富的 REST API 端點，讓開發者能夠快速理解和測試快取管理、會話管理、分散式鎖等功能。

### **主要特色**

- 🌐 **完整 REST API**: 涵蓋所有 common-cache 功能的 API 端點
- 📊 **即時監控**: 內建快取統計和效能監控
- 🔧 **多環境支援**: 開發、測試、生產環境配置
- 🐳 **Docker 就緒**: 完整的容器化部署支援
- 📖 **API 文檔**: 詳細的 Swagger/OpenAPI 文檔
- 🧪 **測試友善**: 包含完整的測試套件和範例

### **功能模組**

| 模組 | 功能 | API 端點 |
|------|------|----------|
| **快取管理** | 基本 CRUD、TTL 管理、批次操作 | `/api/cache/**` |
| **會話管理** | JWT 會話、自動續期、多索引查詢 | `/api/session/**` |
| **分散式鎖** | 公平鎖、讀寫鎖、鎖狀態監控 | `/api/lock/**` |
| **資料清理** | 過期清理、批次清理、統計報告 | `/api/cleaner/**` |
| **系統監控** | 健康檢查、統計資訊、效能指標 | `/api/system/**` |

## 🚀 **快速開始**

### **1. 環境要求**

- **Java 17+**: OpenJDK 或 Oracle JDK
- **Maven 3.6+**: 建置工具
- **Redis 6.0+**: 快取伺服器
- **Docker** (可選): 容器化部署

### **2. 啟動 Redis 服務**

```bash
# 使用 Docker 啟動 Redis
docker run -d \
  --name redis-cache \
  -p 6379:6379 \
  redis:7-alpine

# 或使用 docker-compose
docker-compose up -d redis
```

### **3. 編譯和運行**

```bash
# 克隆專案
git clone <repository-url>
cd Demo-common-cache

# 編譯專案
mvn clean install

# 啟動應用
mvn spring-boot:run

# 或直接運行 JAR
java -jar target/demo-common-cache-1.0.0-SNAPSHOT.jar
```

### **4. 驗證啟動**

```bash
# 檢查應用健康狀態
curl http://localhost:8080/actuator/health

# 檢查 API 文檔
open http://localhost:8080/swagger-ui.html

# 測試基本 API
curl http://localhost:8080/api/system/info
```

### **5. 快速測試**

```bash
# 測試快取功能
curl -X POST "http://localhost:8080/api/cache/user/123" \
  -H "Content-Type: application/json" \
  -d '{"name":"John","email":"<EMAIL>"}'

# 獲取快取資料
curl http://localhost:8080/api/cache/user/123

# 測試會話功能
curl -X POST "http://localhost:8080/api/session/jwt123" \
  -H "Content-Type: application/json" \
  -d '{"userId":"user123","clientId":"web","loginTime":"2024-01-01T10:00:00"}'
```

## 📚 **API 端點文檔**

### **1. 快取管理 API**

#### **儲存快取資料**
```http
POST /api/cache/{type}/{id}
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "age": 30
}
```

**回應範例**:
```json
{
  "success": true,
  "message": "快取資料已儲存",
  "data": {
    "type": "user",
    "id": "123",
    "ttl": 300
  }
}
```

#### **獲取快取資料**
```http
GET /api/cache/{type}/{id}
```

**回應範例**:
```json
{
  "success": true,
  "data": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 30
  },
  "metadata": {
    "ttl": 285,
    "createdAt": "2024-01-01T10:00:00Z"
  }
}
```

#### **刪除快取資料**
```http
DELETE /api/cache/{type}/{id}
```

#### **批次獲取快取**
```http
POST /api/cache/{type}/batch
Content-Type: application/json

{
  "ids": ["123", "456", "789"]
}
```

#### **檢查快取存在**
```http
HEAD /api/cache/{type}/{id}
```

#### **獲取快取 TTL**
```http
GET /api/cache/{type}/{id}/ttl
```

### **2. 會話管理 API**

#### **儲存會話**
```http
POST /api/session/{jwtId}
Content-Type: application/json

{
  "userId": "user123",
  "clientId": "web",
  "loginTime": "2024-01-01T10:00:00",
  "ipAddress": "*************",
  "userAgent": "Mozilla/5.0...",
  "roles": ["USER", "ADMIN"],
  "permissions": ["READ", "WRITE"],
  "attributes": {
    "theme": "dark",
    "language": "zh-TW"
  }
}
```

#### **獲取會話**
```http
GET /api/session/{jwtId}
```

**回應範例**:
```json
{
  "success": true,
  "data": {
    "userId": "user123",
    "clientId": "web",
    "jwtId": "jwt123",
    "loginTime": "2024-01-01T10:00:00Z",
    "lastActiveTime": "2024-01-01T10:30:00Z",
    "status": "ACTIVE",
    "roles": ["USER", "ADMIN"],
    "permissions": ["READ", "WRITE"],
    "attributes": {
      "theme": "dark",
      "language": "zh-TW"
    }
  },
  "metadata": {
    "ttl": 1650,
    "autoRenewal": true
  }
}
```

#### **會話續期**
```http
PUT /api/session/{jwtId}/renew
```

#### **根據使用者 ID 查詢會話**
```http
GET /api/session/user/{userId}
```

#### **根據客戶端 ID 查詢會話**
```http
GET /api/session/client/{clientId}
```

#### **刪除會話**
```http
DELETE /api/session/{jwtId}
```

#### **刪除使用者所有會話**
```http
DELETE /api/session/user/{userId}
```

### **3. 分散式鎖 API**

#### **獲取鎖**
```http
POST /api/lock/{resource}/acquire
Content-Type: application/json

{
  "waitTime": 10,
  "leaseTime": 30,
  "timeUnit": "SECONDS",
  "lockType": "FAIR"
}
```

#### **釋放鎖**
```http
POST /api/lock/{resource}/release
```

#### **檢查鎖狀態**
```http
GET /api/lock/{resource}/status
```

**回應範例**:
```json
{
  "success": true,
  "data": {
    "resource": "order:123",
    "locked": true,
    "heldByCurrentThread": false,
    "lockType": "FAIR",
    "remainingTime": 25
  }
}
```

#### **讀鎖操作**
```http
POST /api/lock/{resource}/read-lock
POST /api/lock/{resource}/read-unlock
```

#### **寫鎖操作**
```http
POST /api/lock/{resource}/write-lock
POST /api/lock/{resource}/write-unlock
```

### **4. 資料清理 API**

#### **清理過期資料**
```http
POST /api/cleaner/clean
Content-Type: application/json

{
  "pattern": "cache:*",
  "batchSize": 100
}
```

**回應範例**:
```json
{
  "success": true,
  "data": {
    "pattern": "cache:*",
    "cleanedCount": 45,
    "batchSize": 100,
    "duration": "1.2s"
  }
}
```

#### **獲取清理統計**
```http
GET /api/cleaner/stats
```

### **5. 系統監控 API**

#### **系統資訊**
```http
GET /api/system/info
```

**回應範例**:
```json
{
  "success": true,
  "data": {
    "application": "Demo Common Cache",
    "version": "1.0.0-SNAPSHOT",
    "buildTime": "2024-01-01T10:00:00Z",
    "javaVersion": "17.0.1",
    "springBootVersion": "3.2.0",
    "redisVersion": "7.0.5",
    "uptime": "2h 30m 15s"
  }
}
```

#### **快取統計**
```http
GET /api/system/cache-stats
```

#### **Redis 資訊**
```http
GET /api/system/redis-info
```

#### **健康檢查**
```http
GET /actuator/health
```

## ⚙️ **配置範例**

### **開發環境配置 (application-dev.yml)**

```yaml
server:
  port: 8080

spring:
  profiles:
    active: dev
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 2

# Common Cache 配置
common:
  cache:
    cache:
      default-time-to-live: 300      # 5 分鐘
      max-idle-time: 600             # 10 分鐘

    session:
      default-time-to-live: 1800     # 30 分鐘
      auto-renewal: true
      renewal-threshold: 0.5

    lock:
      default-wait-time: 10          # 10 秒
      default-lease-time: 30         # 30 秒
      fair-lock-enabled: true

    cleaner:
      enabled: true
      batch-size: 100

    env-guard:
      production-environment: false
      dangerous-operations-enabled: true

# 日誌配置
logging:
  level:
    com.nanshan: DEBUG
    org.redisson: INFO
    org.springframework.data.redis: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

### **測試環境配置 (application-test.yml)**

```yaml
spring:
  profiles:
    active: test
  redis:
    host: localhost
    port: 6380  # 測試專用端口
    database: 1
    timeout: 1000ms

common:
  cache:
    cache:
      default-time-to-live: 60       # 測試用較短 TTL
    session:
      default-time-to-live: 300      # 測試用較短 TTL
    lock:
      default-wait-time: 5
      default-lease-time: 10
    cleaner:
      enabled: true
      batch-size: 50
    env-guard:
      production-environment: false
      dangerous-operations-enabled: true

logging:
  level:
    com.nanshan: TRACE
    org.redisson: DEBUG
```

### **生產環境配置 (application-prod.yml)**

```yaml
server:
  port: 8080

spring:
  profiles:
    active: prod
  redis:
    host: ${REDIS_HOST:redis-cluster}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD}
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 16
        max-idle: 8
        min-idle: 4
        max-wait: 3000ms

common:
  cache:
    cache:
      default-time-to-live: 600      # 10 分鐘
      max-idle-time: 1800            # 30 分鐘

    session:
      default-time-to-live: 3600     # 1 小時
      auto-renewal: true
      renewal-threshold: 0.3

    lock:
      default-wait-time: 15
      default-lease-time: 60
      fair-lock-enabled: true

    cleaner:
      enabled: true
      batch-size: 200

    env-guard:
      production-environment: true   # 生產環境保護
      dangerous-operations-enabled: false  # 禁用危險操作

# 生產環境日誌配置
logging:
  level:
    com.nanshan: INFO
    org.redisson: WARN
    org.springframework.data.redis: WARN
  file:
    name: logs/demo-common-cache.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30

# 生產環境監控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
```

### **Docker 環境配置 (application-docker.yml)**

```yaml
spring:
  profiles:
    active: docker
  redis:
    host: redis  # Docker 服務名稱
    port: 6379
    database: 0

common:
  cache:
    cache:
      default-time-to-live: 300
    session:
      default-time-to-live: 1800
    lock:
      default-wait-time: 10
      default-lease-time: 30
    env-guard:
      production-environment: false
      dangerous-operations-enabled: true

logging:
  level:
    com.nanshan: INFO
```

## 🧪 **測試指南**

### **1. 單元測試**

```bash
# 運行所有單元測試
mvn test

# 運行特定測試類
mvn test -Dtest=CacheControllerTest

# 運行特定測試方法
mvn test -Dtest=CacheControllerTest#testPutCache
```

### **2. 整合測試**

```bash
# 啟動測試 Redis
docker run -d -p 6380:6379 --name redis-test redis:7-alpine

# 運行整合測試
mvn test -Dspring.profiles.active=test

# 清理測試環境
docker stop redis-test && docker rm redis-test
```

### **3. API 測試**

#### **使用 curl 測試**

```bash
# 測試快取 API
curl -X POST "http://localhost:8080/api/cache/user/123" \
  -H "Content-Type: application/json" \
  -d '{"name":"測試用戶","email":"<EMAIL>"}'

curl -X GET "http://localhost:8080/api/cache/user/123"

# 測試會話 API
curl -X POST "http://localhost:8080/api/session/jwt123" \
  -H "Content-Type: application/json" \
  -d '{"userId":"user123","clientId":"web"}'

curl -X GET "http://localhost:8080/api/session/jwt123"

# 測試鎖 API
curl -X POST "http://localhost:8080/api/lock/resource123/acquire" \
  -H "Content-Type: application/json" \
  -d '{"waitTime":10,"leaseTime":30,"timeUnit":"SECONDS"}'

curl -X GET "http://localhost:8080/api/lock/resource123/status"
```

#### **使用 Postman 測試**

1. 匯入 Postman Collection: `postman/Demo-Common-Cache.postman_collection.json`
2. 設定環境變數: `baseUrl = http://localhost:8080`
3. 運行測試集合

#### **使用 JMeter 壓力測試**

```bash
# 運行 JMeter 測試計劃
jmeter -n -t jmeter/cache-performance-test.jmx -l results.jtl

# 生成 HTML 報告
jmeter -g results.jtl -o report/
```

### **4. 自動化測試腳本**

```bash
#!/bin/bash
# test-all.sh

echo "=== 啟動測試環境 ==="
docker-compose -f docker-compose.test.yml up -d

echo "=== 等待服務就緒 ==="
sleep 10

echo "=== 運行單元測試 ==="
mvn test

echo "=== 運行整合測試 ==="
mvn test -Dspring.profiles.active=test

echo "=== 運行 API 測試 ==="
./scripts/api-test.sh

echo "=== 清理測試環境 ==="
docker-compose -f docker-compose.test.yml down

echo "=== 測試完成 ==="
```

## 🐳 **Docker 支援**

### **1. Dockerfile**

```dockerfile
FROM openjdk:17-jdk-slim

LABEL maintainer="Nanshan Team <<EMAIL>>"
LABEL description="Demo Common Cache Application"

WORKDIR /app

# 複製 JAR 檔案
COPY target/demo-common-cache-*.jar app.jar

# 建立非 root 使用者
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 啟動應用
ENTRYPOINT ["java", "-jar", "app.jar"]
```

### **2. docker-compose.yml**

```yaml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: demo-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  demo-app:
    build: .
    container_name: demo-common-cache
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_REDIS_HOST=redis
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  redis-data:

networks:
  default:
    name: demo-cache-network
```

### **3. 容器化部署**

```bash
# 建置映像
docker build -t demo-common-cache:latest .

# 啟動服務
docker-compose up -d

# 檢查服務狀態
docker-compose ps

# 查看日誌
docker-compose logs -f demo-app

# 停止服務
docker-compose down
```

### **4. Kubernetes 部署**

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-common-cache
spec:
  replicas: 3
  selector:
    matchLabels:
      app: demo-common-cache
  template:
    metadata:
      labels:
        app: demo-common-cache
    spec:
      containers:
      - name: demo-app
        image: demo-common-cache:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: SPRING_REDIS_HOST
          value: "redis-service"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: demo-common-cache-service
spec:
  selector:
    app: demo-common-cache
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

## 🔧 **故障排除**

### **1. 應用啟動問題**

#### **問題**: 應用啟動失敗，Redis 連線錯誤

**錯誤訊息**:
```
Unable to connect to Redis server localhost:6379
```

**解決方案**:
```bash
# 檢查 Redis 服務狀態
redis-cli ping

# 如果 Redis 未啟動，使用 Docker 啟動
docker run -d -p 6379:6379 redis:7-alpine

# 檢查配置檔案中的 Redis 設定
# application.yml 中的 spring.redis.host 和 port 是否正確
```

#### **問題**: JSON 解析錯誤

**錯誤訊息**:
```
JsonParseException: Illegal character ((CTRL-CHAR, code 3))
```

**解決方案**:
```bash
# 1. 確認使用最新版本的 common-cache
mvn dependency:tree | grep common-cache

# 2. 清理 Redis 中的損壞資料
redis-cli FLUSHALL

# 3. 重新啟動應用
mvn spring-boot:run
```

### **2. API 測試失敗**

#### **問題**: API 回應 500 錯誤

**解決方案**:
```bash
# 檢查應用日誌
tail -f logs/demo-common-cache.log

# 檢查 Redis 連線狀態
curl http://localhost:8080/actuator/health

# 檢查快取統計
curl http://localhost:8080/api/system/cache-stats
```

#### **問題**: 會話 API 測試失敗

**解決方案**:
```bash
# 檢查會話配置
curl http://localhost:8080/api/system/info

# 手動測試會話儲存
curl -X POST "http://localhost:8080/api/session/test123" \
  -H "Content-Type: application/json" \
  -d '{"userId":"testUser","clientId":"testClient"}'

# 檢查會話是否存在
curl http://localhost:8080/api/session/test123
```

### **3. 效能問題**

#### **問題**: API 回應時間過長

**解決方案**:
```yaml
# 調整 Redis 連線池設定
spring:
  redis:
    lettuce:
      pool:
        max-active: 16
        max-idle: 8
        min-idle: 4

# 調整快取 TTL
common:
  cache:
    cache:
      default-time-to-live: 600  # 增加 TTL 減少重複計算
```

#### **問題**: 記憶體使用過高

**解決方案**:
```bash
# 檢查 JVM 記憶體使用
curl http://localhost:8080/actuator/metrics/jvm.memory.used

# 檢查 Redis 記憶體使用
redis-cli INFO memory

# 調整 JVM 參數
export JAVA_OPTS="-Xmx512m -Xms256m"
mvn spring-boot:run
```

### **4. Docker 部署問題**

#### **問題**: 容器啟動失敗

**解決方案**:
```bash
# 檢查容器日誌
docker-compose logs demo-app

# 檢查網路連線
docker-compose exec demo-app ping redis

# 重新建置映像
docker-compose build --no-cache
docker-compose up -d
```

### **5. 常見錯誤代碼**

| 錯誤代碼 | 說明 | 解決方案 |
|----------|------|----------|
| `CACHE_001` | 快取類型不存在 | 檢查 API 路徑中的 type 參數 |
| `CACHE_002` | 快取 ID 格式錯誤 | 確保 ID 為有效字串 |
| `SESSION_001` | JWT ID 格式錯誤 | 檢查 JWT ID 格式 |
| `SESSION_002` | 會話已過期 | 重新建立會話 |
| `LOCK_001` | 鎖獲取超時 | 增加等待時間或檢查鎖狀態 |
| `LOCK_002` | 鎖已被其他執行緒持有 | 等待鎖釋放或使用不同資源名稱 |

## 📊 **效能測試**

### **1. 基準測試**

#### **快取效能測試**

```bash
# 使用 Apache Bench 測試快取寫入
ab -n 1000 -c 10 -p cache-data.json -T application/json \
  http://localhost:8080/api/cache/user/

# 測試快取讀取
ab -n 1000 -c 10 \
  http://localhost:8080/api/cache/user/123

# 測試結果範例
# Requests per second: 850.23 [#/sec] (mean)
# Time per request: 11.763 [ms] (mean)
```

#### **會話效能測試**

```bash
# 測試會話建立
ab -n 500 -c 5 -p session-data.json -T application/json \
  http://localhost:8080/api/session/

# 測試會話查詢
ab -n 1000 -c 10 \
  http://localhost:8080/api/session/jwt123
```

### **2. 壓力測試**

#### **JMeter 測試計劃**

```xml
<!-- jmeter/cache-stress-test.jmx -->
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan>
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments">
          <elementProp name="baseUrl" elementType="Argument">
            <stringProp name="Argument.name">baseUrl</stringProp>
            <stringProp name="Argument.value">http://localhost:8080</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup>
        <stringProp name="ThreadGroup.num_threads">50</stringProp>
        <stringProp name="ThreadGroup.ramp_time">10</stringProp>
        <stringProp name="ThreadGroup.duration">300</stringProp>
        <!-- 測試計劃詳細配置... -->
      </ThreadGroup>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

#### **運行壓力測試**

```bash
# 運行 JMeter 測試
jmeter -n -t jmeter/cache-stress-test.jmx -l results/stress-test.jtl

# 生成 HTML 報告
jmeter -g results/stress-test.jtl -o results/html-report/

# 查看報告
open results/html-report/index.html
```

### **3. 效能監控**

#### **應用監控**

```bash
# 檢查 JVM 指標
curl http://localhost:8080/actuator/metrics/jvm.memory.used
curl http://localhost:8080/actuator/metrics/jvm.gc.pause

# 檢查 HTTP 請求指標
curl http://localhost:8080/actuator/metrics/http.server.requests

# 檢查快取指標
curl http://localhost:8080/api/system/cache-stats
```

#### **Redis 監控**

```bash
# Redis 效能監控
redis-cli --latency-history -i 1

# Redis 記憶體監控
redis-cli INFO memory

# Redis 連線監控
redis-cli INFO clients
```

### **4. 效能調優建議**

#### **應用層調優**

```yaml
# JVM 參數調優
JAVA_OPTS: >
  -Xmx1g
  -Xms512m
  -XX:+UseG1GC
  -XX:MaxGCPauseMillis=200
  -XX:+HeapDumpOnOutOfMemoryError

# 連線池調優
spring:
  redis:
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms
```

#### **Redis 調優**

```bash
# Redis 配置調優
redis-cli CONFIG SET maxmemory 1gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

### **5. 效能基準**

| 操作類型 | 目標 TPS | 平均延遲 | 95% 延遲 |
|----------|----------|----------|----------|
| 快取寫入 | 800+ | < 15ms | < 50ms |
| 快取讀取 | 1000+ | < 10ms | < 30ms |
| 會話建立 | 500+ | < 20ms | < 60ms |
| 會話查詢 | 800+ | < 15ms | < 40ms |
| 鎖獲取 | 300+ | < 30ms | < 100ms |

## 📈 **監控和告警**

### **1. Prometheus 整合**

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'demo-common-cache'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s
```

### **2. Grafana 儀表板**

```json
{
  "dashboard": {
    "title": "Demo Common Cache Dashboard",
    "panels": [
      {
        "title": "Cache Hit Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(cache_hit_total[5m]) / rate(cache_requests_total[5m])"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_server_requests_seconds_bucket[5m]))"
          }
        ]
      }
    ]
  }
}
```

## 🤝 **貢獻指南**

### **開發環境設定**

```bash
# 克隆專案
git clone <repository-url>
cd Demo-common-cache

# 安裝依賴
mvn clean install

# 啟動開發環境
docker-compose -f docker-compose.dev.yml up -d

# 啟動應用 (開發模式)
mvn spring-boot:run -Dspring.profiles.active=dev
```

### **程式碼規範**

- 遵循 Google Java Style Guide
- 使用 Checkstyle 進行程式碼檢查
- 確保測試覆蓋率 > 80%
- 所有 API 必須有對應的測試

### **提交流程**

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

## 📄 **授權條款**

本專案採用 MIT 授權條款 - 詳見 [LICENSE](LICENSE) 檔案

## 📞 **支援**

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-org/Demo-common-cache/issues)
- 📖 文檔: [API Documentation](http://localhost:8080/swagger-ui.html)
- 💬 討論: [GitHub Discussions](https://github.com/your-org/Demo-common-cache/discussions)

---

**Made with ❤️ by Nanshan Team**
