@echo off
echo "=== Demo Common Cache 日誌測試腳本 ==="
echo.

echo "1. 檢查 Redis 服務狀態..."
redis-cli ping >nul 2>&1
if %errorlevel% neq 0 (
    echo "Redis 服務未運行，正在啟動..."
    docker run -d -p 6379:6379 --name redis-demo redis:7-alpine
    timeout /t 5
) else (
    echo "Redis 服務正常運行"
)

echo.
echo "2. 編譯應用程式..."
mvn clean compile -q

echo.
echo "3. 啟動應用程式（觀察日誌輸出）..."
echo "注意：現在應該不會看到 Redisson DEBUG 日誌了"
echo "按 Ctrl+C 停止應用程式"
echo.

mvn spring-boot:run

echo.
echo "=== 測試完成 ==="
pause
