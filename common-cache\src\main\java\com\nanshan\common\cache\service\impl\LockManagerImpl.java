package com.nanshan.common.cache.service.impl;

import com.nanshan.common.cache.config.CommonCacheProperties;
import com.nanshan.common.cache.exception.LockOperationException;
import com.nanshan.common.cache.service.LockManager;
import com.nanshan.common.cache.util.RedisKeyHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.redisson.api.RLock;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分散式鎖管理實現
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LockManagerImpl implements LockManager {

    private final RedissonClient redissonClient;
    private final CommonCacheProperties cacheProperties;

    /**
     * 獲取預設等待時間
     */
    private long getDefaultWaitTime() {
        return cacheProperties.getLock().getDefaultWaitTime();
    }

    /**
     * 獲取預設租約時間
     */
    private long getDefaultLeaseTime() {
        return cacheProperties.getLock().getDefaultLeaseTime();
    }

    // ==================== 可重入鎖實現 ====================
    @Override
    public void lock(String resource) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey(resource);
            RLock lock = redissonClient.getLock(lockKey);
            lock.lock();
            log.debug("Lock acquired (blocking): resource={}", resource);
        } catch (Exception e) {
            log.error("Failed to lock: resource={}", resource, e);
            throw new LockOperationException(resource, "lock", "Failed to lock", e);
        }
    }

    @Override
    public void lock(String resource, long leaseTime, TimeUnit timeUnit) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey(resource);
            RLock lock = redissonClient.getLock(lockKey);
            lock.lock(leaseTime, timeUnit);
            log.debug("Lock acquired (blocking): resource={}, leaseTime={}s",
                    resource, timeUnit.toSeconds(leaseTime));
        } catch (Exception e) {
            log.error("Failed to lock: resource={}", resource, e);
            throw new LockOperationException(resource, "lock", "Failed to lock", e);
        }
    }

    @Override
    public void lockInterruptibly(String resource) throws InterruptedException {
        try {
            String lockKey = RedisKeyHelper.buildLockKey(resource);
            RLock lock = redissonClient.getLock(lockKey);
            lock.lockInterruptibly();
            log.debug("Lock acquired (interruptibly): resource={}", resource);
        } catch (InterruptedException e) {
            log.debug("Lock acquisition interrupted: resource={}", resource);
            throw e;
        } catch (Exception e) {
            log.error("Failed to lock interruptibly: resource={}", resource, e);
            throw new LockOperationException(resource, "lockInterruptibly",
                    "Failed to lock interruptibly", e);
        }
    }

    @Override
    public void lockInterruptibly(String resource, long leaseTime, TimeUnit timeUnit) throws InterruptedException {
        try {
            String lockKey = RedisKeyHelper.buildLockKey(resource);
            RLock lock = redissonClient.getLock(lockKey);
            lock.lockInterruptibly(leaseTime, timeUnit);
            log.debug("Lock acquired (interruptibly): resource={}, leaseTime={}s",
                    resource, timeUnit.toSeconds(leaseTime));
        } catch (InterruptedException e) {
            log.debug("Lock acquisition interrupted: resource={}", resource);
            throw e;
        } catch (Exception e) {
            log.error("Failed to lock interruptibly: resource={}", resource, e);
            throw new LockOperationException(resource, "lockInterruptibly",
                    "Failed to lock interruptibly", e);
        }
    }

    @Override
    public boolean tryLock(String resource) {
        return tryLock(resource, getDefaultWaitTime(), getDefaultLeaseTime(), TimeUnit.SECONDS);
    }

    @Override
    public boolean tryLock(String resource, long waitTime, long leaseTime, TimeUnit timeUnit) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey(resource);
            RLock lock = redissonClient.getLock(lockKey);

            boolean acquired = lock.tryLock(waitTime, leaseTime, timeUnit);

            if (acquired) {
                log.debug("Lock acquired: resource={}, waitTime={}s, leaseTime={}s",
                        resource, timeUnit.toSeconds(waitTime), timeUnit.toSeconds(leaseTime));
            } else {
                log.debug("Failed to acquire lock: resource={}", resource);
            }

            return acquired;
        } catch (Exception e) {
            log.error("Failed to try lock: resource={}", resource, e);
            throw new LockOperationException(resource, "tryLock",
                    "Failed to try lock", e);
        }
    }

    @Override
    public boolean tryLock(String resource, Duration waitDuration, Duration leaseDuration) {
        return tryLock(resource, waitDuration.toMillis(), leaseDuration.toMillis(),
                TimeUnit.MILLISECONDS);
    }

    @Override
    public boolean unlock(String resource) {
        // 嘗試釋放當前線程持有的所有類型鎖
        boolean unlocked = false;

        // 嘗試釋放可重入鎖
        if (unlock(resource, LockManager.LockType.REENTRANT)) {
            unlocked = true;
        }

        // 嘗試釋放公平鎖
        if (unlock(resource, LockManager.LockType.FAIR)) {
            unlocked = true;
        }

        // 嘗試釋放讀鎖
        if (unlock(resource, LockManager.LockType.READ)) {
            unlocked = true;
        }

        // 嘗試釋放寫鎖
        if (unlock(resource, LockManager.LockType.WRITE)) {
            unlocked = true;
        }

        if (unlocked) {
            log.debug("Successfully unlocked resource: {}", resource);
        } else {
            log.debug("No locks held by current thread for resource: {}", resource);
        }

        return unlocked;
    }

    @Override
    public boolean unlock(String resource, LockManager.LockType lockType) {
        try {
            switch (lockType) {
                case REENTRANT:
                    String lockKey = RedisKeyHelper.buildLockKey(resource);
                    RLock lock = redissonClient.getLock(lockKey);
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                        log.debug("Reentrant lock released: resource={}", resource);
                        return true;
                    }
                    break;

                case FAIR:
                    String fairLockKey = RedisKeyHelper.buildLockKey("fair", resource);
                    RLock fairLock = redissonClient.getFairLock(fairLockKey);
                    if (fairLock.isHeldByCurrentThread()) {
                        fairLock.unlock();
                        log.debug("Fair lock released: resource={}", resource);
                        return true;
                    }
                    break;

                case READ:
                    String rwLockKey = RedisKeyHelper.buildLockKey("rw", resource);
                    RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(rwLockKey);
                    RLock readLock = readWriteLock.readLock();
                    if (readLock.isHeldByCurrentThread()) {
                        readLock.unlock();
                        log.debug("Read lock released: resource={}", resource);
                        return true;
                    }
                    break;

                case WRITE:
                    String rwLockKey2 = RedisKeyHelper.buildLockKey("rw", resource);
                    RReadWriteLock readWriteLock2 = redissonClient.getReadWriteLock(rwLockKey2);
                    RLock writeLock = readWriteLock2.writeLock();
                    if (writeLock.isHeldByCurrentThread()) {
                        writeLock.unlock();
                        log.debug("Write lock released: resource={}", resource);
                        return true;
                    }
                    break;
            }

            log.debug("Lock not held by current thread: resource={}, lockType={}", resource, lockType);
            return false;

        } catch (Exception e) {
            log.error("Failed to unlock: resource={}, lockType={}", resource, lockType, e);
            throw new LockOperationException(resource, "unlock",
                    "Failed to unlock " + lockType + " lock", e);
        }
    }

    @Override
    public boolean forceUnlock(String resource) {
        // 強制釋放所有類型的鎖
        boolean unlocked = false;

        if (forceUnlock(resource, LockManager.LockType.REENTRANT)) {
            unlocked = true;
        }

        if (forceUnlock(resource, LockManager.LockType.FAIR)) {
            unlocked = true;
        }

        if (forceUnlock(resource, LockManager.LockType.READ)) {
            unlocked = true;
        }

        if (forceUnlock(resource, LockManager.LockType.WRITE)) {
            unlocked = true;
        }

        if (unlocked) {
            log.debug("Force unlocked resource: {}", resource);
        } else {
            log.debug("No locks to force unlock for resource: {}", resource);
        }

        return unlocked;
    }

    @Override
    public boolean forceUnlock(String resource, LockManager.LockType lockType) {
        try {
            switch (lockType) {
                case REENTRANT:
                    String lockKey = RedisKeyHelper.buildLockKey(resource);
                    RLock lock = redissonClient.getLock(lockKey);
                    return lock.forceUnlock();

                case FAIR:
                    String fairLockKey = RedisKeyHelper.buildLockKey("fair", resource);
                    RLock fairLock = redissonClient.getFairLock(fairLockKey);
                    return fairLock.forceUnlock();

                case READ:
                    String rwLockKey = RedisKeyHelper.buildLockKey("rw", resource);
                    RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(rwLockKey);
                    return readWriteLock.readLock().forceUnlock();

                case WRITE:
                    String rwLockKey2 = RedisKeyHelper.buildLockKey("rw", resource);
                    RReadWriteLock readWriteLock2 = redissonClient.getReadWriteLock(rwLockKey2);
                    return readWriteLock2.writeLock().forceUnlock();

                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("Failed to force unlock: resource={}, lockType={}", resource, lockType, e);
            throw new LockOperationException(resource, "forceUnlock",
                    "Failed to force unlock " + lockType + " lock", e);
        }
    }

    @Override
    public boolean isLocked(String resource) {
        // 檢查所有可能的鎖類型
        return isLocked(resource, LockManager.LockType.REENTRANT)
                || isLocked(resource, LockManager.LockType.FAIR)
                || isLocked(resource, LockManager.LockType.READ)
                || isLocked(resource, LockManager.LockType.WRITE);
    }

    @Override
    public boolean isLocked(String resource, LockManager.LockType lockType) {
        try {
            switch (lockType) {
                case REENTRANT:
                    String lockKey = RedisKeyHelper.buildLockKey(resource);
                    RLock lock = redissonClient.getLock(lockKey);
                    return lock.isLocked();

                case FAIR:
                    String fairLockKey = RedisKeyHelper.buildLockKey("fair", resource);
                    RLock fairLock = redissonClient.getFairLock(fairLockKey);
                    return fairLock.isLocked();

                case READ:
                    String rwLockKey = RedisKeyHelper.buildLockKey("rw", resource);
                    RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(rwLockKey);
                    return readWriteLock.readLock().isLocked();

                case WRITE:
                    String rwLockKey2 = RedisKeyHelper.buildLockKey("rw", resource);
                    RReadWriteLock readWriteLock2 = redissonClient.getReadWriteLock(rwLockKey2);
                    return readWriteLock2.writeLock().isLocked();

                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("Failed to check if locked: resource={}, lockType={}", resource, lockType, e);
            return false;
        }
    }

    @Override
    public boolean isHeldByCurrentThread(String resource) {
        // 檢查所有可能的鎖類型
        return isHeldByCurrentThread(resource, LockManager.LockType.REENTRANT)
                || isHeldByCurrentThread(resource, LockManager.LockType.FAIR)
                || isHeldByCurrentThread(resource, LockManager.LockType.READ)
                || isHeldByCurrentThread(resource, LockManager.LockType.WRITE);
    }

    @Override
    public boolean isHeldByCurrentThread(String resource, LockManager.LockType lockType) {
        try {
            switch (lockType) {
                case REENTRANT:
                    String lockKey = RedisKeyHelper.buildLockKey(resource);
                    RLock lock = redissonClient.getLock(lockKey);
                    return lock.isHeldByCurrentThread();

                case FAIR:
                    String fairLockKey = RedisKeyHelper.buildLockKey("fair", resource);
                    RLock fairLock = redissonClient.getFairLock(fairLockKey);
                    return fairLock.isHeldByCurrentThread();

                case READ:
                    String rwLockKey = RedisKeyHelper.buildLockKey("rw", resource);
                    RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(rwLockKey);
                    return readWriteLock.readLock().isHeldByCurrentThread();

                case WRITE:
                    String rwLockKey2 = RedisKeyHelper.buildLockKey("rw", resource);
                    RReadWriteLock readWriteLock2 = redissonClient.getReadWriteLock(rwLockKey2);
                    return readWriteLock2.writeLock().isHeldByCurrentThread();

                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("Failed to check if held by current thread: resource={}, lockType={}", resource, lockType, e);
            return false;
        }
    }

    @Override
    public long getRemainingLeaseTime(String resource, TimeUnit timeUnit) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey(resource);
            RLock lock = redissonClient.getLock(lockKey);
            long remainingTimeMillis = lock.remainTimeToLive();

            if (remainingTimeMillis == -1) {
                return -1; // 永不過期
            } else if (remainingTimeMillis == -2) {
                return -2; // 鎖不存在
            } else {
                // 轉換為指定的時間單位
                return timeUnit.convert(remainingTimeMillis, TimeUnit.MILLISECONDS);
            }
        } catch (Exception e) {
            log.error("Failed to get remaining lease time: resource={}", resource, e);
            return -2; // 表示錯誤
        }
    }

    @Override
    public boolean tryFairLock(String resource) {
        return tryFairLock(resource, getDefaultWaitTime(), getDefaultLeaseTime(), TimeUnit.SECONDS);
    }

    @Override
    public boolean tryFairLock(String resource, long waitTime, long leaseTime, TimeUnit timeUnit) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("fair", resource);
            RLock fairLock = redissonClient.getFairLock(lockKey);

            boolean acquired = fairLock.tryLock(waitTime, leaseTime, timeUnit);

            if (acquired) {
                log.debug("Fair lock acquired: resource={}, waitTime={}s, leaseTime={}s",
                        resource, timeUnit.toSeconds(waitTime), timeUnit.toSeconds(leaseTime));
            } else {
                log.debug("Failed to acquire fair lock: resource={}", resource);
            }

            return acquired;
        } catch (Exception e) {
            log.error("Failed to try fair lock: resource={}", resource, e);
            throw new LockOperationException(resource, "tryFairLock",
                    "Failed to try fair lock", e);
        }
    }

    @Override
    public boolean tryReadLock(String resource) {
        return tryReadLock(resource, getDefaultWaitTime(), getDefaultLeaseTime(), TimeUnit.SECONDS);
    }

    @Override
    public boolean tryReadLock(String resource, long waitTime, long leaseTime, TimeUnit timeUnit) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("rw", resource);
            RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
            RLock readLock = readWriteLock.readLock();

            boolean acquired = readLock.tryLock(waitTime, leaseTime, timeUnit);

            if (acquired) {
                log.debug("Read lock acquired: resource={}, waitTime={}s, leaseTime={}s",
                        resource, timeUnit.toSeconds(waitTime), timeUnit.toSeconds(leaseTime));
            } else {
                log.debug("Failed to acquire read lock: resource={}", resource);
            }

            return acquired;
        } catch (Exception e) {
            log.error("Failed to try read lock: resource={}", resource, e);
            throw new LockOperationException(resource, "tryReadLock",
                    "Failed to try read lock", e);
        }
    }

    @Override
    public boolean tryWriteLock(String resource) {
        return tryWriteLock(resource, getDefaultWaitTime(), getDefaultLeaseTime(), TimeUnit.SECONDS);
    }

    @Override
    public boolean tryWriteLock(String resource, long waitTime, long leaseTime, TimeUnit timeUnit) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("rw", resource);
            RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
            RLock writeLock = readWriteLock.writeLock();

            boolean acquired = writeLock.tryLock(waitTime, leaseTime, timeUnit);

            if (acquired) {
                log.debug("Write lock acquired: resource={}, waitTime={}s, leaseTime={}s",
                        resource, timeUnit.toSeconds(waitTime), timeUnit.toSeconds(leaseTime));
            } else {
                log.debug("Failed to acquire write lock: resource={}", resource);
            }

            return acquired;
        } catch (Exception e) {
            log.error("Failed to try write lock: resource={}", resource, e);
            throw new LockOperationException(resource, "tryWriteLock",
                    "Failed to try write lock", e);
        }
    }

    @Override
    public void fairLock(String resource) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("fair", resource);
            RLock fairLock = redissonClient.getFairLock(lockKey);

            fairLock.lock();
            log.debug("Fair lock acquired: resource={}", resource);
        } catch (Exception e) {
            log.error("Failed to acquire fair lock: resource={}", resource, e);
            throw new LockOperationException(resource, "fairLock",
                    "Failed to acquire fair lock", e);
        }
    }

    @Override
    public void fairLock(String resource, long leaseTime, TimeUnit timeUnit) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("fair", resource);
            RLock fairLock = redissonClient.getFairLock(lockKey);

            fairLock.lock(leaseTime, timeUnit);
            log.debug("Fair lock acquired: resource={}, leaseTime={}s",
                    resource, timeUnit.toSeconds(leaseTime));
        } catch (Exception e) {
            log.error("Failed to acquire fair lock: resource={}", resource, e);
            throw new LockOperationException(resource, "fairLock",
                    "Failed to acquire fair lock", e);
        }
    }

    @Override
    public void fairLockInterruptibly(String resource) throws InterruptedException {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("fair", resource);
            RLock fairLock = redissonClient.getFairLock(lockKey);

            fairLock.lockInterruptibly();
            log.debug("Fair lock acquired interruptibly: resource={}", resource);
        } catch (InterruptedException e) {
            log.debug("Fair lock acquisition interrupted: resource={}", resource);
            throw e;
        } catch (Exception e) {
            log.error("Failed to acquire fair lock interruptibly: resource={}", resource, e);
            throw new LockOperationException(resource, "fairLockInterruptibly",
                    "Failed to acquire fair lock interruptibly", e);
        }
    }

    @Override
    public void fairLockInterruptibly(String resource, long leaseTime, TimeUnit timeUnit) throws InterruptedException {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("fair", resource);
            RLock fairLock = redissonClient.getFairLock(lockKey);

            fairLock.lockInterruptibly(leaseTime, timeUnit);
            log.debug("Fair lock acquired interruptibly: resource={}, leaseTime={}s",
                    resource, timeUnit.toSeconds(leaseTime));
        } catch (InterruptedException e) {
            log.debug("Fair lock acquisition interrupted: resource={}", resource);
            throw e;
        } catch (Exception e) {
            log.error("Failed to acquire fair lock interruptibly: resource={}", resource, e);
            throw new LockOperationException(resource, "fairLockInterruptibly",
                    "Failed to acquire fair lock interruptibly", e);
        }
    }

    @Override
    public void readLock(String resource) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("rw", resource);
            RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
            RLock readLock = readWriteLock.readLock();

            readLock.lock();
            log.debug("Read lock acquired: resource={}", resource);
        } catch (Exception e) {
            log.error("Failed to acquire read lock: resource={}", resource, e);
            throw new LockOperationException(resource, "readLock",
                    "Failed to acquire read lock", e);
        }
    }

    @Override
    public void readLock(String resource, long leaseTime, TimeUnit timeUnit) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("rw", resource);
            RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
            RLock readLock = readWriteLock.readLock();

            readLock.lock(leaseTime, timeUnit);
            log.debug("Read lock acquired: resource={}, leaseTime={}s",
                    resource, timeUnit.toSeconds(leaseTime));
        } catch (Exception e) {
            log.error("Failed to acquire read lock: resource={}", resource, e);
            throw new LockOperationException(resource, "readLock",
                    "Failed to acquire read lock", e);
        }
    }

    @Override
    public void readLockInterruptibly(String resource) throws InterruptedException {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("rw", resource);
            RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
            RLock readLock = readWriteLock.readLock();

            readLock.lockInterruptibly();
            log.debug("Read lock acquired interruptibly: resource={}", resource);
        } catch (InterruptedException e) {
            log.debug("Read lock acquisition interrupted: resource={}", resource);
            throw e;
        } catch (Exception e) {
            log.error("Failed to acquire read lock interruptibly: resource={}", resource, e);
            throw new LockOperationException(resource, "readLockInterruptibly",
                    "Failed to acquire read lock interruptibly", e);
        }
    }

    @Override
    public void readLockInterruptibly(String resource, long leaseTime, TimeUnit timeUnit) throws InterruptedException {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("rw", resource);
            RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
            RLock readLock = readWriteLock.readLock();

            readLock.lockInterruptibly(leaseTime, timeUnit);
            log.debug("Read lock acquired interruptibly: resource={}, leaseTime={}s",
                    resource, timeUnit.toSeconds(leaseTime));
        } catch (InterruptedException e) {
            log.debug("Read lock acquisition interrupted: resource={}", resource);
            throw e;
        } catch (Exception e) {
            log.error("Failed to acquire read lock interruptibly: resource={}", resource, e);
            throw new LockOperationException(resource, "readLockInterruptibly",
                    "Failed to acquire read lock interruptibly", e);
        }
    }

    @Override
    public void writeLock(String resource) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("rw", resource);
            RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
            RLock writeLock = readWriteLock.writeLock();

            writeLock.lock();
            log.debug("Write lock acquired: resource={}", resource);
        } catch (Exception e) {
            log.error("Failed to acquire write lock: resource={}", resource, e);
            throw new LockOperationException(resource, "writeLock",
                    "Failed to acquire write lock", e);
        }
    }

    @Override
    public void writeLock(String resource, long leaseTime, TimeUnit timeUnit) {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("rw", resource);
            RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
            RLock writeLock = readWriteLock.writeLock();

            writeLock.lock(leaseTime, timeUnit);
            log.debug("Write lock acquired: resource={}, leaseTime={}s",
                    resource, timeUnit.toSeconds(leaseTime));
        } catch (Exception e) {
            log.error("Failed to acquire write lock: resource={}", resource, e);
            throw new LockOperationException(resource, "writeLock",
                    "Failed to acquire write lock", e);
        }
    }

    @Override
    public void writeLockInterruptibly(String resource) throws InterruptedException {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("rw", resource);
            RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
            RLock writeLock = readWriteLock.writeLock();

            writeLock.lockInterruptibly();
            log.debug("Write lock acquired interruptibly: resource={}", resource);
        } catch (InterruptedException e) {
            log.debug("Write lock acquisition interrupted: resource={}", resource);
            throw e;
        } catch (Exception e) {
            log.error("Failed to acquire write lock interruptibly: resource={}", resource, e);
            throw new LockOperationException(resource, "writeLockInterruptibly",
                    "Failed to acquire write lock interruptibly", e);
        }
    }

    @Override
    public void writeLockInterruptibly(String resource, long leaseTime, TimeUnit timeUnit) throws InterruptedException {
        try {
            String lockKey = RedisKeyHelper.buildLockKey("rw", resource);
            RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);
            RLock writeLock = readWriteLock.writeLock();

            writeLock.lockInterruptibly(leaseTime, timeUnit);
            log.debug("Write lock acquired interruptibly: resource={}, leaseTime={}s",
                    resource, timeUnit.toSeconds(leaseTime));
        } catch (InterruptedException e) {
            log.debug("Write lock acquisition interrupted: resource={}", resource);
            throw e;
        } catch (Exception e) {
            log.error("Failed to acquire write lock interruptibly: resource={}", resource, e);
            throw new LockOperationException(resource, "writeLockInterruptibly",
                    "Failed to acquire write lock interruptibly", e);
        }
    }

}
